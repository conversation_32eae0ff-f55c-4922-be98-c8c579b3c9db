{"name": "302_prompt_generator", "path": "/Users/<USER>/workspace/promptworkspace/302_prompt_generator", "workspace_id": "9072d93096f6e552ec3e1259145def91", "conversations": [{"id": "cd322631-1578-46bc-8906-47cfd82cae19", "name": "", "created_at": "2025-04-26T14:50:54.387Z", "last_interacted_at": "2025-04-26T14:50:54.387Z", "messages": []}, {"id": "6fea8964-682d-4f10-8ce0-199e3a4f9acd", "name": "", "created_at": "2025-04-26T14:50:54.394Z", "last_interacted_at": "2025-04-26T14:51:16.395Z", "messages": [{"request_id": "a963257b-25aa-416f-9559-aaa17f06dfbf", "request_message": "Provide a short summary of this project", "response_text": "This is an AI Prompt Expert tool that helps users transform simple prompts into high-quality structured prompts using various frameworks. Here are the key points:\n\n1. Core Features:\n- Supports 12 different prompt optimization frameworks including CO-STAR, CRISPE, QStar(Q*), Chain of Thought (CoT), and others\n- Allows online editing and testing of prompts\n- Can optimize text-to-image prompts\n- One-click conversion to high-quality English prompts\n\n2. Technical Stack:\n- Frontend: React, TypeScript, Tailwind CSS, Radix UI\n- Containerization: Docker\n- API Integration with 302.AI services\n\n3. Language Support:\n- Chinese\n- English\n- Japanese\n\n4. Deployment Options:\n- Can be used directly on 302.AI platform (zero-code, zero-config)\n- Can be self-hosted with 302.AI API key\n\nThe project is open-sourced by 302.AI and designed to help users create more effective prompts for various AI models and use cases.\n", "timestamp": "2025-04-26T14:51:16.417Z", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/promptworkspace/302_prompt_generator", "path_name": "README.md", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 71, "endColumn": 135}}, {"repo_root": "/Users/<USER>/workspace/promptworkspace/302_prompt_generator", "path_name": "README_zh.md", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 68, "endColumn": 31}}, {"repo_root": "/Users/<USER>/workspace/promptworkspace/302_prompt_generator", "path_name": "src/App.tsx", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 26, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/promptworkspace/302_prompt_generator", "path_name": "src/App.tsx", "full_range": {"startLineNumber": 77, "startColumn": 0, "endLineNumber": 136, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/promptworkspace/302_prompt_generator", "path_name": "src/App.tsx", "full_range": {"startLineNumber": 256, "startColumn": 0, "endLineNumber": 273, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/promptworkspace/302_prompt_generator", "path_name": "src/App.tsx", "full_range": {"startLineNumber": 471, "startColumn": 0, "endLineNumber": 497, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/promptworkspace/302_prompt_generator", "path_name": "src/App.tsx", "full_range": {"startLineNumber": 525, "startColumn": 0, "endLineNumber": 549, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/promptworkspace/302_prompt_generator", "path_name": "src/App.tsx", "full_range": {"startLineNumber": 724, "startColumn": 0, "endLineNumber": 747, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/promptworkspace/302_prompt_generator", "path_name": "src/App.tsx", "full_range": {"startLineNumber": 776, "startColumn": 0, "endLineNumber": 814, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/promptworkspace/302_prompt_generator", "path_name": "src/App.tsx", "full_range": {"startLineNumber": 981, "startColumn": 0, "endLineNumber": 999, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/promptworkspace/302_prompt_generator", "path_name": "README_ja.md", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 74, "endColumn": 36}}, {"repo_root": "/Users/<USER>/workspace/promptworkspace/302_prompt_generator", "path_name": "index.html", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 17, "endColumn": 7}}, {"repo_root": "/Users/<USER>/workspace/promptworkspace/302_prompt_generator", "path_name": "vite.config.ts", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 13, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/promptworkspace/302_prompt_generator", "path_name": "src/main.tsx", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 27, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/promptworkspace/302_prompt_generator", "path_name": ".env.example", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 21, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/promptworkspace/302_prompt_generator", "path_name": "Dockerfile", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 26, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/promptworkspace/302_prompt_generator", "path_name": "src/lib/Language.ts", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 29, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/promptworkspace/302_prompt_generator", "path_name": "package.json", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 36, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/promptworkspace/302_prompt_generator", "path_name": "src/index.css", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 30, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/promptworkspace/302_prompt_generator", "path_name": "package-lock.json", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 31, "endColumn": 0}}, {"repo_root": "/Users/<USER>/workspace/promptworkspace/302_prompt_generator", "path_name": "src/lib/AnalyzeImagesPrompt.ts", "full_range": {"startLineNumber": 0, "startColumn": 0, "endLineNumber": 27, "endColumn": 0}}]}]}, {"id": "4fe73054-8a20-4bc1-9509-8589cb16408b", "name": "", "created_at": "2025-04-26T14:51:26.525Z", "last_interacted_at": "2025-04-26T14:53:31.167Z", "messages": [{"request_id": "0baf4713-c52e-4e99-bc69-6ae74be4c6a6", "request_message": "", "response_text": "", "timestamp": "", "workspace_files": []}, {"request_id": "7aadaf3d-82b4-49a8-b07a-7dfc03f8297b", "request_message": "仔细分析这个项目实现的主要功能，为我梳理项目文件的结构依赖，借助于 PRD的编写逻辑为我输出对应的用户故事", "response_text": "我会帮您分析这个项目的主要功能、文件结构依赖，并基于PRD编写逻辑为您输出用户故事。首先，让我们通过检索工具来了解项目的结构和功能。\n", "timestamp": "2025-04-26T14:52:36.823Z", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/promptworkspace/302_prompt_generator", "path_name": "src/lib/Language.ts", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "919717c1-abee-4187-b077-c0115e283ba8", "request_message": "", "response_text": "让我继续了解更多关系到项目功能的文件：\n", "timestamp": "2025-04-26T14:52:50.428Z", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/promptworkspace/302_prompt_generator", "path_name": "src/lib/Language.ts", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "5d1c3d7a-54e8-403f-94fd-34bf93823520", "request_message": "", "response_text": "让我重试一下：\n", "timestamp": "2025-04-26T14:53:01.707Z", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/promptworkspace/302_prompt_generator", "path_name": "src/lib/AnalyzeImagesPrompt.ts", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "844a4ca1-3a5c-40b4-8c7a-12280ae6c2ca", "request_message": "", "response_text": "让我查看一下项目的主要功能和结构：\n", "timestamp": "2025-04-26T14:53:15.661Z", "workspace_files": [{"repo_root": "/Users/<USER>/workspace/promptworkspace/302_prompt_generator", "path_name": "src/lib/AnalyzeImagesPrompt.ts", "full_range": {"startLineNumber": 1, "startColumn": 0, "endLineNumber": 1, "endColumn": 0}}]}, {"request_id": "1c7c28e5-5524-41e3-a6da-6ce7784b2a84", "request_message": "", "response_text": "现在我对项目有了更全面的了解，让我为您梳理项目的主要功能和文件结构依赖，并基于PRD编写逻辑输出用户故事。\n\n## 项目分析\n\n### 主要功能\n\n这个项目是一个AI提示词专家工具，主要功能包括：\n\n1. **提示词优化与生成**：将用户简单的提示语改写成高质量的结构化提示词\n2. **多种提示词结构支持**：支持CO-STAR、CRISPE、QStar(Q*)、变分法、Meta Prompting、CoT思维链、微软优化法和RISE等多种提示词结构\n3. **在线修改和测试**：用户可以在线修改生成的提示词并进行测试\n4. **图像描述生成**：支持上传图片，生成高质量的图像描述提示词\n5. **多语言支持**：支持中文、英文和日文三种语言界面\n\n### 文件结构依赖\n\n项目的主要文件结构和依赖关系如下：\n\n1. **核心文件**：\n   - `src/App.tsx`：主应用组件，包含主要的UI和功能逻辑\n   - `src/lib/prompt.ts`：提示词模板和处理逻辑\n   - `src/lib/Language.ts`：多语言支持配置\n   - `src/lib/AnalyzeImagesPrompt.ts`：图像分析提示词生成\n\n2. **状态管理**：\n   - `src/store/index.ts`：Redux store配置\n   - `src/store/globalSlice.ts`：全局状态管理，主要处理语言设置\n   - `src/store/hooks.ts`：Redux hooks\n\n3. **UI组件**：\n   - `src/components/Header.tsx`：页面头部组件\n   - `src/components/LanguagePopover.tsx`：语言选择组件\n   - `src/components/PoweredBy.tsx`：品牌展示组件\n   - `src/components/ui/`：基础UI组件库\n\n4. **工具函数**：\n   - `src/utils.ts`：通用工具函数，包括API请求、错误处理等\n\n5. **入口文件**：\n   - `src/main.tsx`：应用入口，配置Redux Provider和路由\n   - `index.html`：HTML入口文件\n\n## 用户故事\n\n基于对项目的分析，以下是按照PRD编写逻辑输出的用户故事：\n\n### 1. 基础提示词生成\n\n**作为**一名AI内容创作者，  \n**我希望**能够将我的简单任务描述转换为结构化的高质量提示词，  \n**以便**我能够获得更好的AI生成内容质量。\n\n**验收标准**：\n- 用户可以在输入框中输入简单的任务描述\n- 系统能够根据选择的结构类型生成对应的高质量提示词\n- 生成的提示词应当结构清晰，包含必要的上下文、目标、身份等要素\n- 生成过程中有加载状态提示\n\n### 2. 多种提示词结构选择\n\n**作为**一名提示词工程师，  \n**我希望**能够选择不同的提示词结构框架，  \n**以便**针对不同类型的任务使用最适合的提示词结构。\n\n**验收标准**：\n- 用户可以从下拉菜单中选择不同的提示词结构（CO-STAR、CRISPE、DRAW等）\n- 每种结构有简短的说明文字，帮助用户理解其适用场景\n- 选择不同结构后，生成的提示词应符合该结构的特点\n- 用户可以保存自己的自定义提示词结构\n\n### 3. 提示词修改与测试\n\n**作为**一名内容创作者，  \n**我希望**能够修改生成的提示词并立即测试效果，  \n**以便**我能够不断优化提示词直到满意。\n\n**验收标准**：\n- 用户可以在生成提示词后进行编辑修改\n- 用户可以点击\"测试\"按钮，使用当前提示词获取AI回复\n- 测试结果应当清晰展示，便于用户评估提示词效果\n- 用户可以基于测试结果继续修改提示词\n\n### 4. 图像描述提示词生成\n\n**作为**一名AI绘画爱好者，  \n**我希望**能够上传图片并获取专业的图像描述提示词，  \n**以便**我能够用于文本生成图像的场景。\n\n**验收标准**：\n- 用户可以上传图片文件\n- 系统能够分析图片并生成详细的描述提示词\n- 生成的提示词应包含图像的关键元素、风格、构图等信息\n- 用户可以复制生成的提示词用于其他平台\n\n### 5. 多语言界面支持\n\n**作为**一名非中文母语的用户，  \n**我希望**能够切换界面语言为我熟悉的语言，  \n**以便**我能够更好地理解和使用工具功能。\n\n**验收标准**：\n- 用户可以通过语言选择器切换界面语言（中文、英文、日文）\n- 所有UI元素、按钮文本、提示信息应当正确翻译\n- 语言设置应当被保存，下次访问时自动应用\n- 语言切换不应影响当前的工作内容\n\n### 6. 提示词复制与分享\n\n**作为**一名团队协作者，  \n**我希望**能够方便地复制生成的提示词，  \n**以便**我能够与团队成员分享或在其他平台使用。\n\n**验收标准**：\n- 用户可以通过点击复制按钮一键复制生成的提示词\n- 复制成功后有明确的成功提示\n- 复制的内容应保留原有格式，便于在其他平台粘贴使用\n\n### 7. 自定义提示词优化指令\n\n**作为**一名高级提示词工程师，  \n**我希望**能够创建和使用自定义的提示词优化指令，  \n**以便**我能够根据特定需求定制提示词生成逻辑。\n\n**验收标准**：\n- 用户可以选择\"自定义\"结构类型\n- 用户可以编辑自定义提示词优化指令\n- 系统能够保存用户的自定义指令供后续使用\n- 自定义指令可以使用占位符引用用户输入的任务\n\n### 8. 错误处理与反馈\n\n**作为**一名普通用户，  \n**我希望**在使用过程中遇到错误时能够获得清晰的错误提示，  \n**以便**我能够理解问题并采取相应措施。\n\n**验收标准**：\n- 系统在API调用失败时显示友好的错误提示\n- 错误信息应当根据用户选择的语言显示\n- 用户输入验证应当及时提供反馈（如空输入提示）\n- 网络问题或服务不可用时有明确的提示信息\n\n这些用户故事涵盖了项目的主要功能和用户需求，可以作为产品开发和测试的基础。每个故事都包含了用户角色、需求和验收标准，符合PRD的编写逻辑。\n", "timestamp": "2025-04-26T14:53:31.171Z", "workspace_files": []}]}]}