# 2025-05-25_01-09-09

**项目**: PromptX

**创建时间**: 2025年05月25日 01:09

**最后交互**: 2025年05月25日 01:12

**对话ID**: 683a7358-8785-40d8-b595-c0b8e1965302

---

### 对话 1

> **👤 用户** (2025年05月25日 01:09)

Provide a short summary of this project

> **🤖 Augment** (2025年05月25日 01:09)

# PromptX 项目概述

PromptX 是一个系统性、工程化的提示词管理框架，提供结构化、模块化的方式来构建和管理 AI 提示词。主要特点：

- 使用 DPML (Deepractice Prompt Markup Language) 作为标准化提示词表达框架
- 提供角色、思维模式、执行框架和记忆系统的结构化组织
- 支持通过 `bootstrap.md` 和 `Action` 指令快速加载和激活角色
- 包含多种预定义角色（产品负责人、提示词开发者、视频文案专家等）
- 提供可视化表达（思维导图、流程图等）支持思考过程

框架通过标签化结构（XML风格）和内容（Markdown）相结合的方式，使提示词具有清晰的语义结构和自然语言表达能力，便于模块化组织和复用。


---

### 对话 2

> **👤 用户** (2025年05月25日 01:12)

什么是PromptX，主要功能和用途是什么？ 核心架构是什么，如何组织和管理提示词的？

> **🤖 Augment** (2025年05月25日 01:12)

# PromptX 简介

PromptX 是一个系统性、工程化的提示词管理框架，它提供了结构化、模块化的方式来构建和管理 AI 提示词。

## 主要功能和用途

- **提示词结构化管理**：将提示词按照特定协议组织，便于复用和维护
- **角色定制**：内置多种专业角色，如产品负责人、提示词开发者等
- **思维增强**：通过思维模式强化 AI 的分析能力和逻辑严谨性
- **行为规范化**：通过执行模式确保 AI 按照标准流程完成任务
- **记忆系统**：让 AI 具备学习和记忆能力，实现上下文感知

## 核心架构

PromptX 基于 DPML (Deepractice Prompt Markup Language) 协议，通过以下核心组件组织提示词：

1. **思维模式 (Thought)**：定义 AI 如何思考问题
   - 探索 (exploration)：发散思维，多角度分析
   - 推理 (reasoning)：逻辑分析和因果关系
   - 计划 (plan)：行动方案和执行路径
   - 挑战 (challenge)：自我质疑和风险识别

2. **执行模式 (Execution)**：规范 AI 的行为
   - 流程 (process)：标准化执行步骤
   - 指南 (guideline)：最佳实践和建议
   - 规则 (rule)：必须遵循的约束
   - 限制 (constraint)：边界条件和限制

3. **记忆模式 (Memory)**：管理 AI 的知识和经验
   - 知识库 (knowledge)：预设领域知识
   - 评估 (evaluate)：记忆价值判断
   - 存储 (store)：记忆内容保存
   - 回忆 (recall)：检索历史记忆

4. **角色组合**：通过 XML 标签将上述组件组合成完整角色

PromptX 通过文件系统组织提示词，使用 `@file://` 语法引用其他提示词文件，实现模块化和复用。


---

