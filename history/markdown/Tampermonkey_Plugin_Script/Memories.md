# Tampermonkey_Plugin_Script 的记忆

# Feature Request: zsxq_download_file.js Enhancements
- User wants zsxq_download_file.js to support both JSON and Markdown export formats with a new export format selection option.
- User wants zsxq_download_file.js to have a simplified Markdown export with only a table that includes file_id and api_url columns.

# Rich Text Content Analysis
- Analyze rich text content: Use regular expressions to parse text marked with <e type="..."> tags, extracting elements such as titles, links, and labels.