# 2025-05-25_00-55-27

**项目**: cursor-view

**创建时间**: 2025年05月25日 00:55

**最后交互**: 2025年05月25日 00:58

**对话ID**: 504b3326-483e-4d1e-888c-e01e6c1a9785

---

### 对话 1

---

### 对话 2

> **👤 用户** (2025年05月25日 00:56)

帮我使用 firecraw 工具获取  https://www.woshipm.com/ai/6220258.html 这个链接的内容转换成markdown, 为我梳理出其中的核心流程，以及关键点

> **🤖 Augment** (2025年05月25日 00:56)

我来帮您使用 Firecrawl 工具获取该链接的内容并转换成 Markdown 格式，然后为您梳理核心流程和关键点。


基于获取到的内容，我来为您梳理这篇文章的核心流程和关键点：

## 文章核心内容梳理

### 主题概述
这是一篇关于**AI驱动产品开发实践**的文章，作者分享了如何使用AI工具（主要是Amazon Q）来完成从需求分析到产品落地的全流程开发。

### 核心流程（10个步骤）

#### 1. **需求拆解阶段**
- **工具**：Amazon Q
- **目标**：理解赛题/需求的核心要素
- **关键点**：使用结构化Prompt让AI分析问题定义、目标澄清、评判标准、约束条件、用户视角

#### 2. **头脑风暴阶段**
- **目标**：从多角度发散产品创意
- **关键点**：从用户视角、技术可行性、场景拓展、商业增长、已有方案优化等维度进行思路发散

#### 3. **用户洞察阶段**
- **目标**：深入理解用户痛点和需求
- **关键点**：分析高频问题、用户群画像、潜在痛点挖掘、机会点识别

#### 4. **功能梳理阶段**
- **目标**：将产品想法拆解为具体功能需求
- **关键点**：明确功能清单、用户操作路径、功能间逻辑关系、MVP收敛

#### 5. **PRD文档生成**
- **目标**：生成AI可读的产品需求文档
- **关键点**：结构化输出，包含产品背景、功能模块、用户流程、输入输出、接口需求等

#### 6. **界面设计理解**
- **工具**：Figma MCP服务
- **目标**：让AI理解设计稿的视觉和交互逻辑
- **关键点**：通过Figma链接让AI自动识别页面结构、组件类型、交互关系

#### 7. **API文档准备**
- **目标**：为AI提供系统对接能力
- **关键点**：准备清晰的接口说明、参数规则、请求方式

#### 8. **代码实现**
- **目标**：AI根据文档自动生成代码
- **关键点**：遵循设计规范（如Tailwind CSS），使用CDN图标和Unsplash图片

#### 9. **产品介绍文档**
- **目标**：生成专业的产品说明材料
- **关键点**：包含产品概述、目标用户、核心功能、使用场景、技术架构等

#### 10. **营销文案生成**
- **目标**：为不同平台生成推广文案
- **关键点**：根据平台特点调整语气和风格，明确核心卖点和CTA

### 关键技术工具

1. **Amazon Q**：主要的AI助手工具
2. **Figma MCP**：用于读取设计稿
3. **Flowith**：快速验证产品想法和demo
4. **其他AI IDE工具**：Cursor、Windsurf等

### 核心价值观点

1. **AI时代人人都是产品经理**：AI工具大大降低了产品开发的技术门槛
2. **从想法到成品的无缝衔接**：传统的多轮沟通流程被简化为自然语言描述
3. **AI原生应用的重新定义**：不是在原有软件上叠加AI功能，而是从底层重新定义交互方式

### 实际案例展示

- **Pailido**：基于多模态AI的拍立得相机应用
- **语音笔记**：通过iOS快捷指令实现的语音转文字工具
- **小红书运营工具箱**：使用Flowith快速验证的产品demo

这篇文章的核心价值在于提供了一套完整的、可操作的AI辅助产品开发流程，让产品经理能够在AI时代更高效地将创意转化为实际产品。


---

### 对话 3

> **👤 用户** (2025年05月25日 00:57)

帮我整理出其中每个流程的核心提示词，不要有任何遗漏

> **🤖 Augment** (2025年05月25日 00:57)

基于文章内容，我来为您整理出每个流程的核心提示词，确保不遗漏任何细节：

## AI驱动产品开发的10个核心提示词

### 1. 需求拆解阶段提示词

```
我现在有一份赛题内容，我希望你能像一位产品策略专家一样帮我逐步拆解它的核心要素。请你按照以下逻辑来分析

问题定义：这份题目想解决的核心问题是什么？能不能帮我总结成一句话的问题描述？

目标澄清：最终希望达到的结果是什么？目标是产品交付、用户增长，还是解决某个痛点？

评判标准：有没有明确的评分维度或隐含的成功标准？比如技术可行性、商业价值、用户体验等。

约束条件：有没有给出特定的使用范围、时间限制、技术框架或者团队配置要求？

用户视角：从用户角度出发，这个题目服务的核心人群是谁？他们有什么痛点或需求？

帮我用结构化的方式回答，最好每点用简洁直白的语言概括清楚，适合后续直接拿去写 PRD 或进入头脑风暴阶段。
```

### 2. 头脑风暴阶段提示词

```
接下来我想请你帮我进行一轮头脑风暴，围绕我们已经明确的赛题方向，帮我从不同角度提出一些可能的产品创意或解决思路。

请你尝试从以下几个维度来发散，每个维度列出多个可探索的方向或场景：

用户视角：用户是谁？他们的痛点可能在哪？有没有什么新的使用场景可以挖掘？

技术可行性：基于当前主流的大模型、API、工具，能不能有技术上的新用法或组合？

场景拓展：除了直接解决问题的方案，有没有什么"借力"的方式？比如结合已有平台、已有数据流？

商业与增长：有没有什么设计能兼顾转化率、用户留存、商业模式？

已有方案优化：现在市面上有没有类似解决方案？能不能改造升级、差异化突破？

如果可以，也帮我用一句话总结每个创意背后的核心逻辑。目标不是一步到位，而是尽可能多地打开思路，把盲区点亮。
```

### 3. 用户洞察阶段提示词

```
我现在有一批资料（包括用户反馈、调研记录、竞品分析报告等等），信息量比较大，也比较杂，我想请你像一个懂产品和用户研究的分析师一样，帮我把这些内容整理出清晰的洞察结果。请从下面这些角度来分析：

高频问题归类：哪些问题或需求是被反复提到的？能不能帮我分成几个主题或类目？

用户群画像：根据提问或反馈的内容，哪些用户群体最关注这些问题？有没有可以细分的人群特征？

潜在痛点挖掘：有没有哪些隐含的问题不是直接被说出来的，但可以从表述中读出背后的需求？

机会点识别：从用户的角度看，有哪些功能是他们觉得不方便、但市面上的竞品也没做好的？我们有没有机会切入？

体验差异化建议：有没有什么需求点是可以作为我们产品的差异化亮点来设计的？

最后，请你用结构化的方式输出这些分析结果，最好能分点呈现，便于我下一步进入功能梳理和产品方案设计阶段。
```

### 4. 功能梳理阶段提示词

```
我现在有一个初步的产品构想（或者一个明确的目标/场景），我希望你能根据我们前面的分析结果，帮我整理出这个产品应该包含的核心功能。请从下面几个维度来帮我一步步梳理出来：

功能清单：这个产品从"用户第一次使用"到"完成主要任务"应该包括哪些主要功能模块？请列出每一块并简单解释下它是干嘛的。

用户操作路径：站在用户的角度，整个使用过程大概是怎么一步步走下来的？每个功能点出现在哪个环节？

每个功能解决什么问题：帮我对齐一下：每个功能点的设计目的是什么？它具体是在解决哪个用户痛点或需求？

功能之间的逻辑关系：有没有依赖关系、优先级？哪些是核心功能，哪些是辅助功能？

MVP视角收敛：如果我要先做一个最小可用版本（MVP），应该保留哪些功能？可以先放弃哪些？

请把回答整理成结构清晰的段落或表格，方便我直接拿去做 PRD 或和团队沟通设计方案。
```

### 5. PRD文档生成提示词

```
我已经有了产品的基本构想和功能列表，现在想请你帮我整理成一份AI 能看懂也能继续执行的 PRD（产品需求文档）。这份 PRD 不仅要让人看得懂，更要考虑结构的清晰度、格式的规范性，方便后续接入自动化流程或智能工具调用。请按以下结构来输出：

产品背景和目标：简单描述产品要解决什么问题，服务什么场景，核心目标是什么。

核心功能模块列表：列出每个功能模块的名称、功能描述、使用场景，最好补充一句"这个功能解决的问题"。

用户流程图（文字版）：从用户视角出发，描述用户从进入产品到完成关键任务的操作路径（可分步骤标序号）。

每个功能的输入/输出：列清楚用户或系统输入了什么、期望的输出结果是什么，适合后续和大模型交互或和 API 对接。

接口需求（如有）：说明每个功能点是否需要接口支持，如果需要，说明接口用途、调用方式、数据结构要求。

边界条件和异常处理：列举常见的边界情况和异常场景，说明系统应如何响应。

请按模块清晰排版，内容尽量结构化，避免散乱描述，最好用 Markdown 或表格格式输出，便于后续被其他智能工具或工程团队复用。

最后保存到当前文件夹中，以md格式存储。
```

### 6. 界面设计理解提示词

```
结合我的figma设计稿链接（https://www.figma.com/design/WXpW4QN4xFIeDVdZ7uqppm/Minimalistic-Social-Media-Templates–Community-?node-id=29-1239&t=fyrkseULN35IUmeL-4），读取我的prd文档，帮我实现这个功能，遵循Tailwind Css设计规范，使用CND图标替换，使用Unspalsh图片替换对应的图片信息。
```

### 7. API文档准备提示词

```
我需要为AI准备API文档，让它能够理解和调用相关接口。请帮我整理一份清晰的API文档，包含以下内容：

接口说明：每个接口的用途和功能描述
参数规则：输入参数的类型、格式、必填项说明
请求方式：HTTP方法、请求路径、请求头要求
响应格式：返回数据的结构和字段说明
错误处理：常见错误码和处理方式
调用示例：具体的请求和响应示例

请确保文档结构清晰，便于AI理解和自动调用。
```

### 8. 代码实现提示词

```
现在请根据我们的PRD文档、API文档和设计稿，开始实现产品功能。请遵循以下要求：

1. 严格按照PRD文档中的功能需求进行开发
2. 遵循Tailwind CSS设计规范
3. 使用CDN图标库替换设计稿中的图标
4. 使用Unsplash图片替换占位图片
5. 确保代码结构清晰，注释完整
6. 实现响应式设计，适配不同设备
7. 处理边界条件和异常情况

请开始编写代码并确保功能完整可用。
```

### 9. 产品介绍文档提示词

```
我现在有一个已经完成的产品，我希望你根据之前的需求文档、用户场景和功能说明，帮我生成一份清晰的产品介绍文档。这个文档的目标是让外部或内部团队快速理解我们产品的核心价值、功能和使用方式。请按照以下结构来写：

产品概述：简短介绍产品是什么，解决什么问题，面向什么样的用户，简洁地传达产品的核心价值。

目标用户群体：说明该产品的主要用户是谁，如何满足他们的需求，为什么它对这些用户有吸引力。

核心功能：列出并简要描述产品的主要功能，每个功能点用一两句话概述它的用途和目标。

使用场景：给出几个典型的用户使用场景或使用流程，帮助理解产品是如何在实际环境中解决问题的。

技术架构和创新点：简要说明产品的技术亮点、创新之处，或是产品的独特技术实现，帮助突出产品的竞争力。

市场定位和竞争优势：分析产品在市场上的定位，以及相对于竞品的优势和差异化。

未来发展和优化方向：简单介绍一下产品的未来发展计划，未来可能的功能拓展或优化方向。

请保持文档结构清晰、重点突出，不啰嗦，语言简洁明了，适合用作内部汇报、外部介绍或者评审材料。
```

### 10. 营销文案生成提示词

```
我现在需要一份针对产品的营销推广文案。请你根据我提供的背景信息和目标，帮我写出适合不同平台发布的文案，确保文案能够吸引目标用户，并在适当的语气和风格上有所调整。以下是文案的要求：

平台：文案将发布在哪些平台？比如官网、微博、微信公众号、抖音、小红书等。根据平台特点，调整文案风格和语气。

目标受众：我的目标用户是谁？他们的需求、兴趣、痛点是什么？（比如年轻人、企业管理者、技术人员等）

文案类型：你需要写哪种类型的文案？
- 正式的介绍文案：适合官网、产品手册、企业宣传等正式场合。
- 轻松的社交媒体文案：适合微博、朋友圈、短视频平台等互动性较强的渠道。

核心卖点：产品的哪些功能或优势最值得突出？（例如节省时间、提高效率、降低成本、创新性强等）

号召性用语（CTA）：希望用户看到文案后采取什么行动？例如点击购买、注册体验、了解更多等。

请根据这些要求生成文案，确保不同平台和受众有相应的风格变化。如果能提供多个版本（例如短版和长版），就更好了。
```

## 补充说明

### MCP配置提示词
```
vi ~/.aws/amazonq/mcp.json
```
创建MCP配置文件，添加Figma MCP服务配置。

### 检查MCP服务提示词
```
请告诉我目前有哪些MCP服务是可以使用的？
```

这10个核心提示词涵盖了从需求分析到产品上线的完整流程，每个提示词都经过精心设计，融合了产品管理的专业方法论，确保AI能够提供高质量的输出结果。


---

### 对话 4

> **👤 用户** (2025年05月25日 00:58)

分析每个流程的提示词的作用，关键点

> **🤖 Augment** (2025年05月25日 00:58)

我来详细分析每个流程提示词的作用和关键点：

## 提示词深度分析

### 1. 需求拆解阶段提示词

**作用**：将复杂的需求文档转化为结构化的产品策略要素
**关键点分析**：
- **角色设定**："像一位产品策略专家" - 建立专业身份认知
- **结构化思维**：5W1H框架的变体（What/Why/Who/When/How/Where）
- **输出导向**："适合后续直接拿去写PRD" - 明确下游应用场景
- **语言要求**："简洁直白的语言" - 避免AI过度发散

**核心方法论**：
- Design Thinking的问题定义阶段
- 产品管理中的需求澄清框架
- 战略分析中的SMART原则

### 2. 头脑风暴阶段提示词

**作用**：从多维度系统性地发散创意思路
**关键点分析**：
- **多维度发散**：用户、技术、场景、商业、竞品 - 确保思考全面性
- **创意激发**："打开思路，把盲区点亮" - 突破思维局限
- **逻辑提炼**："一句话总结核心逻辑" - 保持创意的可执行性
- **平衡控制**："目标不是一步到位" - 避免过早收敛

**核心方法论**：
- SCAMPER创意技法（替代、组合、调整等）
- 五力模型的多维分析
- 第一性原理思考

### 3. 用户洞察阶段提示词

**作用**：从杂乱信息中提炼有价值的用户洞察
**关键点分析**：
- **信息整理**："信息量比较大，也比较杂" - 承认现实复杂性
- **专业角色**："懂产品和用户研究的分析师" - 建立专业分析视角
- **洞察层次**：从显性问题到隐性需求的递进分析
- **机会识别**：竞品空白点的发现机制
- **差异化导向**：为产品定位提供依据

**核心方法论**：
- Affinity Mapping（相似性归类）
- Jobs to be Done理论
- Pain-Gain Map分析法

### 4. 功能梳理阶段提示词

**作用**：将抽象产品构想转化为具体功能需求
**关键点分析**：
- **全流程视角**："从第一次使用到完成主要任务" - 确保功能完整性
- **用户路径**：以用户操作为主线串联功能
- **问题映射**：每个功能都要对应明确的用户痛点
- **优先级管理**：核心功能vs辅助功能的区分
- **MVP思维**：最小可行产品的功能收敛

**核心方法论**：
- User Journey Mapping
- Function-Need映射表
- MoSCoW优先级模型

### 5. PRD文档生成提示词

**作用**：生成机器可读、人类可理解的标准化需求文档
**关键点分析**：
- **双重可读性**："AI能看懂也能继续执行" - 考虑后续自动化流程
- **结构标准化**：模块化、表格化的输出要求
- **接口导向**：明确输入输出，便于系统对接
- **异常处理**：边界条件的预先考虑
- **格式规范**：Markdown格式便于版本管理和协作

**核心方法论**：
- 机器友好型文档设计
- 产品六要素框架
- API-First的设计思维

### 6. 界面设计理解提示词

**作用**：让AI理解视觉设计并转化为可执行的代码规范
**关键点分析**：
- **设计稿解读**：通过Figma MCP服务自动识别设计元素
- **技术规范**：明确使用Tailwind CSS框架
- **资源替换**：CDN图标和Unsplash图片的标准化处理
- **简洁高效**：一句话完成复杂的设计到代码的转换

**核心方法论**：
- Design-to-Code自动化
- 组件化设计思维
- 标准化资源管理

### 7. API文档准备提示词

**作用**：为AI提供系统集成的技术接口规范
**关键点分析**：
- **完整性**：涵盖接口的所有关键要素
- **标准化**：遵循RESTful API文档规范
- **可执行性**：提供具体的调用示例
- **错误处理**：预设异常情况的处理方案

**核心方法论**：
- OpenAPI规范
- API-First设计原则
- 接口文档标准化

### 8. 代码实现提示词

**作用**：将所有前期准备转化为可运行的产品代码
**关键点分析**：
- **多文档整合**：PRD、API、设计稿的综合应用
- **技术约束**：明确的技术栈和规范要求
- **质量标准**：代码结构、注释、响应式设计等要求
- **完整性**：从功能实现到异常处理的全覆盖

**核心方法论**：
- 敏捷开发实践
- 代码质量管理
- 响应式设计原则

### 9. 产品介绍文档提示词

**作用**：生成面向不同受众的产品说明材料
**关键点分析**：
- **多受众适配**："外部或内部团队" - 考虑不同阅读需求
- **价值传达**：从产品概述到竞争优势的完整价值链
- **场景化描述**：通过使用场景增强理解
- **未来导向**：发展规划体现产品的成长性
- **专业性**：适合正式场合的语言风格

**核心方法论**：
- 产品营销4P理论
- 价值主张设计
- 商业计划书结构

### 10. 营销文案生成提示词

**作用**：为不同平台和受众生成个性化的推广内容
**关键点分析**：
- **平台适配**：根据不同平台特点调整风格
- **受众细分**：针对不同用户群体的个性化表达
- **文案分层**：正式vs轻松的风格区分
- **转化导向**：明确的CTA（Call to Action）设计
- **版本多样性**：长版短版满足不同使用场景

**核心方法论**：
- 多渠道营销策略
- 用户画像驱动的个性化营销
- AIDA营销漏斗模型

## 整体设计原则分析

### 1. **渐进式复杂度**
从简单的需求理解到复杂的代码实现，每个阶段的复杂度递增，符合认知负荷理论。

### 2. **上下文传承**
每个提示词都考虑了前一阶段的输出，形成完整的信息传递链条。

### 3. **专业角色扮演**
通过角色设定（策略专家、分析师等）提升AI的专业性和输出质量。

### 4. **结构化输出**
所有提示词都强调结构化、模块化的输出格式，便于后续处理和应用。

### 5. **实用性导向**
每个提示词都明确了输出结果的具体用途，确保生成内容的实际价值。

### 6. **质量控制机制**
通过明确的要求和约束条件，确保AI输出符合专业标准。

这套提示词体系的核心价值在于将传统的产品开发流程进行了AI化改造，既保持了专业性，又大幅提升了效率。


---

