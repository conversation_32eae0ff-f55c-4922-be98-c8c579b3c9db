# 2025-04-26_14-51-26

**项目**: 302_prompt_generator

**创建时间**: 2025年04月26日 14:51

**最后交互**: 2025年04月26日 14:53

**对话ID**: 4fe73054-8a20-4bc1-9509-8589cb16408b

---

### 对话 1

---

### 对话 2

> **👤 用户** (2025年04月26日 14:52)

仔细分析这个项目实现的主要功能，为我梳理项目文件的结构依赖，借助于 PRD的编写逻辑为我输出对应的用户故事

> **🤖 Augment** (2025年04月26日 14:52)

我会帮您分析这个项目的主要功能、文件结构依赖，并基于PRD编写逻辑为您输出用户故事。首先，让我们通过检索工具来了解项目的结构和功能。


让我继续了解更多关系到项目功能的文件：


让我重试一下：


让我查看一下项目的主要功能和结构：


现在我对项目有了更全面的了解，让我为您梳理项目的主要功能和文件结构依赖，并基于PRD编写逻辑输出用户故事。

## 项目分析

### 主要功能

这个项目是一个AI提示词专家工具，主要功能包括：

1. **提示词优化与生成**：将用户简单的提示语改写成高质量的结构化提示词
2. **多种提示词结构支持**：支持CO-STAR、CRISPE、QStar(Q*)、变分法、Meta Prompting、CoT思维链、微软优化法和RISE等多种提示词结构
3. **在线修改和测试**：用户可以在线修改生成的提示词并进行测试
4. **图像描述生成**：支持上传图片，生成高质量的图像描述提示词
5. **多语言支持**：支持中文、英文和日文三种语言界面

### 文件结构依赖

项目的主要文件结构和依赖关系如下：

1. **核心文件**：
   - `src/App.tsx`：主应用组件，包含主要的UI和功能逻辑
   - `src/lib/prompt.ts`：提示词模板和处理逻辑
   - `src/lib/Language.ts`：多语言支持配置
   - `src/lib/AnalyzeImagesPrompt.ts`：图像分析提示词生成

2. **状态管理**：
   - `src/store/index.ts`：Redux store配置
   - `src/store/globalSlice.ts`：全局状态管理，主要处理语言设置
   - `src/store/hooks.ts`：Redux hooks

3. **UI组件**：
   - `src/components/Header.tsx`：页面头部组件
   - `src/components/LanguagePopover.tsx`：语言选择组件
   - `src/components/PoweredBy.tsx`：品牌展示组件
   - `src/components/ui/`：基础UI组件库

4. **工具函数**：
   - `src/utils.ts`：通用工具函数，包括API请求、错误处理等

5. **入口文件**：
   - `src/main.tsx`：应用入口，配置Redux Provider和路由
   - `index.html`：HTML入口文件

## 用户故事

基于对项目的分析，以下是按照PRD编写逻辑输出的用户故事：

### 1. 基础提示词生成

**作为**一名AI内容创作者，  
**我希望**能够将我的简单任务描述转换为结构化的高质量提示词，  
**以便**我能够获得更好的AI生成内容质量。

**验收标准**：
- 用户可以在输入框中输入简单的任务描述
- 系统能够根据选择的结构类型生成对应的高质量提示词
- 生成的提示词应当结构清晰，包含必要的上下文、目标、身份等要素
- 生成过程中有加载状态提示

### 2. 多种提示词结构选择

**作为**一名提示词工程师，  
**我希望**能够选择不同的提示词结构框架，  
**以便**针对不同类型的任务使用最适合的提示词结构。

**验收标准**：
- 用户可以从下拉菜单中选择不同的提示词结构（CO-STAR、CRISPE、DRAW等）
- 每种结构有简短的说明文字，帮助用户理解其适用场景
- 选择不同结构后，生成的提示词应符合该结构的特点
- 用户可以保存自己的自定义提示词结构

### 3. 提示词修改与测试

**作为**一名内容创作者，  
**我希望**能够修改生成的提示词并立即测试效果，  
**以便**我能够不断优化提示词直到满意。

**验收标准**：
- 用户可以在生成提示词后进行编辑修改
- 用户可以点击"测试"按钮，使用当前提示词获取AI回复
- 测试结果应当清晰展示，便于用户评估提示词效果
- 用户可以基于测试结果继续修改提示词

### 4. 图像描述提示词生成

**作为**一名AI绘画爱好者，  
**我希望**能够上传图片并获取专业的图像描述提示词，  
**以便**我能够用于文本生成图像的场景。

**验收标准**：
- 用户可以上传图片文件
- 系统能够分析图片并生成详细的描述提示词
- 生成的提示词应包含图像的关键元素、风格、构图等信息
- 用户可以复制生成的提示词用于其他平台

### 5. 多语言界面支持

**作为**一名非中文母语的用户，  
**我希望**能够切换界面语言为我熟悉的语言，  
**以便**我能够更好地理解和使用工具功能。

**验收标准**：
- 用户可以通过语言选择器切换界面语言（中文、英文、日文）
- 所有UI元素、按钮文本、提示信息应当正确翻译
- 语言设置应当被保存，下次访问时自动应用
- 语言切换不应影响当前的工作内容

### 6. 提示词复制与分享

**作为**一名团队协作者，  
**我希望**能够方便地复制生成的提示词，  
**以便**我能够与团队成员分享或在其他平台使用。

**验收标准**：
- 用户可以通过点击复制按钮一键复制生成的提示词
- 复制成功后有明确的成功提示
- 复制的内容应保留原有格式，便于在其他平台粘贴使用

### 7. 自定义提示词优化指令

**作为**一名高级提示词工程师，  
**我希望**能够创建和使用自定义的提示词优化指令，  
**以便**我能够根据特定需求定制提示词生成逻辑。

**验收标准**：
- 用户可以选择"自定义"结构类型
- 用户可以编辑自定义提示词优化指令
- 系统能够保存用户的自定义指令供后续使用
- 自定义指令可以使用占位符引用用户输入的任务

### 8. 错误处理与反馈

**作为**一名普通用户，  
**我希望**在使用过程中遇到错误时能够获得清晰的错误提示，  
**以便**我能够理解问题并采取相应措施。

**验收标准**：
- 系统在API调用失败时显示友好的错误提示
- 错误信息应当根据用户选择的语言显示
- 用户输入验证应当及时提供反馈（如空输入提示）
- 网络问题或服务不可用时有明确的提示信息

这些用户故事涵盖了项目的主要功能和用户需求，可以作为产品开发和测试的基础。每个故事都包含了用户角色、需求和验收标准，符合PRD的编写逻辑。


---

