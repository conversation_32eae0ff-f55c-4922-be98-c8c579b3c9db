# 2025-04-26_14-50-54

**项目**: 302_prompt_generator

**创建时间**: 2025年04月26日 14:50

**最后交互**: 2025年04月26日 14:51

**对话ID**: 6fea8964-682d-4f10-8ce0-199e3a4f9acd

---

### 对话 1

> **👤 用户** (2025年04月26日 14:51)

Provide a short summary of this project

> **🤖 Augment** (2025年04月26日 14:51)

This is an AI Prompt Expert tool that helps users transform simple prompts into high-quality structured prompts using various frameworks. Here are the key points:

1. Core Features:
- Supports 12 different prompt optimization frameworks including CO-STAR, CRISPE, QStar(Q*), Chain of Thought (CoT), and others
- Allows online editing and testing of prompts
- Can optimize text-to-image prompts
- One-click conversion to high-quality English prompts

2. Technical Stack:
- Frontend: React, TypeScript, Tailwind CSS, Radix UI
- Containerization: Docker
- API Integration with 302.AI services

3. Language Support:
- Chinese
- English
- Japanese

4. Deployment Options:
- Can be used directly on 302.AI platform (zero-code, zero-config)
- Can be self-hosted with 302.AI API key

The project is open-sourced by 302.AI and designed to help users create more effective prompts for various AI models and use cases.


---

