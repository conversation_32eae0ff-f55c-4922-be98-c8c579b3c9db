# git-prompt 的记忆

# Category Display
- On the homepage, display real category data sorted by count in descending order, showing only the top six categories.

# User Interface Preferences
- User prefers exploration and categorization navigation elements to be positioned on the left side of the interface.
- User prefers responses in Chinese language.
- 用户偏好分析应聚焦在提示词详情页的核心内容，不包括主图和导航栏。
- 用户希望了解提示词详情页面的设计模式，关注页面结构和功能布局而非内容差异。
- 用户希望在优化提示词详情页面时保留现有功能，如提示词演化树。
- 用户偏好在讨论设计方案时不需要提供具体代码实现。
- 用户偏好在提示词详情页面的标题下方简洁展示统计指标（star数、fork数、收藏数），使用现有图标，并要求显示真实数据包括时间，并在第二行展示标签，紧接着展示描述。
- 用户偏好在开始编码前先提供详细的设计方案。
- 用户偏好将卡片和列表的切换组件放在左侧，过滤组件放在右侧。
- 用户反馈视图切换按钮在选中状态下可见性差，需要改进按钮的选中状态样式。
- 用户偏好将视图切换组件移动到分类下面，整体布局从上到下依次为：搜索框、分类、视图切换组件、过滤组件（时间和排序）。
- 用户偏好在PromptsListView.tsx组件中描述信息默认只显示一行，多余的省略，以保持列表的高度一致。
- 用户偏好搜索框左对齐布局。
- 除了个人主页的提示词卡片，其他页面的提示词卡片和列表不需要显示Fork标签。
- 用户希望将搜索按钮改为'+'号，用于代表创建提示词功能，且'+'号按钮有适当的背景以凸显重要性，点击后应支持多选功能，目前仅有Prompt选项，未来可扩展其他选项。
- 用户希望提示词卡片上的评论按钮保持不可点击状态。

# Project Context
- I am analyzing a different project and need to carefully read the current project's code instead of relying on contextual information.
- 用户考虑将项目后端数据库迁移至MySQL，需要评估可行性和难度。
- 用户希望移除DataPreloader组件和React Query缓存策略，确保每次直接查询数据库，以便后续进行针对性优化。
- 用户要求在重构和优化代码时必须确保不影响现有的已测试通过的功能.
- 用户偏好直接修改实际代码而非仅提供代码建议。
- 用户希望将大型组件文件（如PromptDetail.tsx）拆分成多个较小的文件以提高代码可维护性。

# User Avatars
- User avatars in the application are retrieved from the profiles table in Supabase, with fallbacks to generated avatars using DiceBear.

# Prompt Detail Page & Prompt Usage
- 用户已经完成了PromptDetail.tsx组件的渲染优化，使用了React.memo、useCallback和useMemo提高性能。
- 用户希望右侧功能区使用提示词功能（即现有的'使用AI'功能），去掉自定义选项，直接在使用提示词卡片展示变量、选择模型等表单元素。
- 用户建议'使用提示词'功能可以完全参考现有的'使用AI'功能来实现。
- User prefers to have model selection form elements positioned above the 'Send Prompt' button in the prompt usage interface.
- 用户希望在提示词详情页面的右侧功能区保留作者信息卡片。
- 用户不需要在提示词详情页面的右侧功能区包含通用参数设置（如语言、风格、语调）。

# Model Management
- 模型设置页面应保存可用的提供商及其对应的模型列表，测试连接时应以最新数据覆盖原有数据。
- 模型设置应使用单一数据结构gitprompt_model_settings存储服务商及其对应的模型列表，以确保数据的原子性和一致性。
- 模型选择数据在页面刷新后丢失，需要解决模型元数据的持久化存储问题。
- 用户希望模型选择界面支持先选择服务商，然后级联显示对应的模型列表，同时下拉框应支持搜索查询功能。
- 用户希望模型选择界面只显示已保存的服务商，并且在实现前先检查localStorage中的数据。
- 当模型设置中不存在可用的服务商或模型时，应显示设置按钮并允许用户跳转到模型设置页面。

# Documentation
- User prefers documentation to be maintained in both Chinese and English languages.
- 用户希望完善整个项目的文档，注重全面的文档覆盖。

# Prompt Statistics
- 在统计提示词相关数据时，删除的提示词不应计算在统计范围内。
- 在数据库中，提示词的删除状态是通过 state = 1 字段表示，而不是 is_deleted 字段。