# prompt-architect 的记忆

# System Design
- User wants to design a system that integrates AIHubMix API with the Prompt Architect functionality.
- User wants to develop the Prompt Architect as part of an AI prompt sharing platform for discovering, creating, and sharing AI prompts.
- User prefers complete HTML+CSS+JS implementations with files in the root directory for direct access.
- User prefers the system to read prompt content from local files rather than embedding them in the code.
- User wants to use AIHubMix API (baseurl: https://aihubmix.com/v1) instead of OpenAI, with a hardcoded API key, and prefers to fetch available models via the /models endpoint.
- User wants to optimize prompts for website cloning with Firecrawl MCP tools and create detailed guides that allow AI tools to perform high-fidelity website cloning from just a URL.
- User prefers tasks to be completed step by step rather than all at once.
- User wants modern website design for the website analysis tool.
- User wants detailed developer documentation for the website analysis tool.
- User prefers Supabase for data storage and wants detailed documentation with UI/UX specifications for both frontend and backend.

# Project Documentation
- User prefers complete PRD documentation format for project specifications without technical implementation details.
- User prefers PRD documentation to focus on product overview, core features, user experience, product value, implementation roadmap, and success metrics, with emphasis on non-technical aspects and detailed information.
`