# rulebook-ai 的记忆

# Rulebook-AI System
- User wants to make AI's mode recognition and rule application visible to users (white-box instead of black-box) in the Rulebook-AI system.
- User prefers responses in Chinese language.
- User wants to package the Rulebook-AI project into an installable local command-line tool.
- 用户希望将Rulebook-AI项目集成为VSCode插件，需要分析可行性和实现方案。
- 用户希望项目提供中文版文档（README_zh.md）。
- 用户希望了解如何将Rulebook-AI项目打包并上传到外部仓库（如PyPI）以便直接安装。

# Dependency Management
- User关注依赖管理，倾向于只安装必要的依赖而非requirements.txt中的所有包。
- 在Rulebook-AI项目中实现了分层依赖管理，将依赖分为核心和可选模块(web/search/llm/test)，优化了打包逻辑