# 2025-05-11_03-43-31

**项目**: rulebook-ai

**创建时间**: 2025年05月11日 03:43

**最后交互**: 2025年05月14日 13:30

**对话ID**: f03774bc-1678-4e69-93e6-e40e57ce2dda

---

### 对话 1

---

### 对话 2

> **👤 用户** (2025年05月11日 03:44)

分析这个项目，为我梳理这个项目实现的主要功能，解决的预期问题，又是如何做到的 ？

> **🤖 Augment** (2025年05月11日 03:44)

我将为您分析这个项目，梳理其主要功能、解决的问题以及实现方式。首先，让我通过检索项目的关键文件来了解项目的整体结构和功能。


让我继续检索更多关于项目的实现细节和功能：


让我继续检索更多关于项目的规则集和实现细节：


让我继续检索关于项目的工具和内存系统的更多信息：


让我检索一下项目的目录结构和实际使用示例：


让我查看一下项目的主要文件：


基于我对项目的分析，现在我将为您梳理这个项目的主要功能、解决的问题以及实现方式。

# Rulebook-AI 项目分析

## 主要功能

Rulebook-AI 是一个通用规则模板系统，旨在为AI编码助手（如Cursor、CLINE、RooCode、Windsurf和Github Copilot）提供一致的行为和上下文。项目的主要功能包括：

1. **跨平台规则统一**：为不同的AI编码助手提供统一的规则集，确保在不同平台上的AI行为一致性。

2. **持久化项目记忆**：通过结构化的文档系统（"Memory Bank"）为AI提供深度上下文，包括项目需求、架构、技术决策和进度。

3. **工作流程标准化**：定义清晰的工作流程（规划、实现、调试），确保AI在不同任务阶段遵循最佳实践。

4. **项目管理支持**：通过任务跟踪和上下文管理，帮助开发团队更好地与AI协作。

5. **工具集成**：提供与LLM API、网页抓取等工具的集成，扩展AI助手的能力。

## 解决的问题

Rulebook-AI 解决了以下关键问题：

1. **AI行为不一致问题**：不同AI编码助手在没有明确指导的情况下可能表现不一致，导致开发体验不佳。

2. **上下文丢失问题**：传统AI助手难以维持长期项目上下文，每次交互都需要重新解释项目背景。

3. **最佳实践缺失**：确保AI遵循软件工程最佳实践，而不是生成可能有问题的代码。

4. **跨平台兼容性**：解决在不同AI编码平台之间切换时的上下文和工作流程不连贯问题。

5. **项目复杂性管理**：帮助AI理解和导航复杂项目的架构和依赖关系。

## 实现方式

Rulebook-AI 通过以下方式实现其功能：

### 1. 规则集系统

项目提供了三种不同详细程度的规则集：

- **heavy-spec**：高度详细和规范的规则集
- **medium-spec**：平衡详细度和原则性指导的简化版本
- **light-spec**：专注于核心原则的最简洁版本

每个规则集都包含：
- 元规则（00-meta-rules.md）：决定操作焦点（规划/实现/调试）
- 一般原则（06-rules_v1.md）：定义最佳实践
- 工作流规则：针对规划（01-plan_v1.md）、实现（01-code_v1.md）和调试（01-debug_v1.md）的具体工作流程

### 2. 内存系统（Memory Bank）

项目实现了一个结构化的"内存银行"系统，作为AI的持久化上下文：

**核心文件（必需）**：
- `product_requirement_docs.md`：产品需求文档，定义项目目的和目标
- `architecture.md`：系统架构文档，概述系统设计和组件关系
- `technical.md`：技术规范文档，详述开发环境和技术决策
- `tasks_plan.md`：任务积压和项目进度跟踪
- `active_context.md`：当前开发上下文，记录当前工作重点和最近变更

**上下文文件（可选）**：
- `error-documentation.md`：错误文档，记录已知问题和解决方案
- `lessons-learned.md`：经验教训，记录项目智能和模式
- `docs/literature/`：研究和参考材料
- `tasks/rfc/`：功能请求和设计提案

### 3. 管理工具

项目提供了`manage_rules.py`脚本来管理规则集和内存系统：

- **install**：将选定的规则集、内存启动器和工具启动器复制到目标仓库
- **sync**：同步平台规则，为不同平台（Cursor、CLINE、RooCode、Windsurf、Github Copilot）生成适当格式的规则文件
- **list-rules**：列出可用的规则集
- **clean-rules**：清理目标仓库中的规则文件

### 4. 目录结构标准

项目定义了一个标准化的目录结构：

```
项目根目录
├── memory/             # 项目内存银行
│   ├── docs/           # 项目文档
│   └── tasks/          # 任务计划和活动上下文
├── .cursor/rules/      # Cursor的自定义规则
├── .clinerules         # CLINE的自定义规则
├── .roo                # RooCode的自定义规则
├── .windsurfrules      # Windsurf的自定义规则
├── .github/copilot-instructions.md # Github Copilot的自定义规则
├── src/                # 主源代码
├── test/               # 测试套件
├── utils/              # 实用工具或库
├── config/             # 配置文件
└── data/               # 数据资源
```

### 5. 工具集成

项目提供了与各种工具的集成：

- **LLM API**：与OpenAI、Anthropic、Google等LLM提供商的API集成
- **网页抓取**：用于从网页获取和提取文本内容的工具
- **截图验证**：用于捕获网页截图并使用LLM验证其外观的工作流程

## 工作流程

Rulebook-AI定义了三种主要工作流程：

1. **规划模式（PLAN/Architect）**：
   - 读取内存文件
   - 验证上下文
   - 制定策略
   - 提出方法
   - 验证方法
   - 在内存文件中记录

2. **实现模式（ACT/Code）**：
   - 检查内存文件
   - 更新文档
   - 执行任务
   - 记录变更

3. **调试模式（DEBUG）**：
   - 分析问题
   - 查找根本原因
   - 提出解决方案
   - 实施修复
   - 验证修复
   - 记录经验教训

## 总结

Rulebook-AI 是一个创新的项目，通过结合软件工程最佳实践和结构化文档系统，为AI编码助手提供了一个强大的框架。它解决了AI编码助手在复杂项目中的一致性、上下文和最佳实践问题，使AI能够更有效地参与软件开发过程。

该项目的核心价值在于：
1. 提供了跨平台的一致AI行为
2. 建立了持久化的项目记忆系统
3. 强制执行软件工程最佳实践
4. 优化了令牌使用
5. 与最新版本的AI助手兼容

通过这种方式，Rulebook-AI 使开发人员能够更有效地与AI编码助手协作，从而提高开发效率和代码质量。


---

### 对话 3

> **👤 用户** (2025年05月11日 03:51)

帮我分析 规则集系统 解释每个规则集包含的规则，仔细阅读每个规则文件，解释每个规则背后主要的意义和作用是什么？ 在AI编程中起到的效果是什么？

> **🤖 Augment** (2025年05月11日 03:51)

我将为您分析规则集系统，解释每个规则集包含的规则及其背后的意义和作用。首先，让我检索更多关于规则文件的详细信息。


让我检查一下轻量级规则集和重量级规则集的关键文件，以便比较不同规则集的差异：


现在，我将对规则集系统进行详细分析，解释每个规则的意义和作用，以及它们在AI编程中的效果。

# 规则集系统分析

Rulebook-AI项目提供了三种不同详细程度的规则集（heavy-spec、medium-spec和light-spec），每个规则集都包含一系列规则文件，这些文件共同定义了AI助手在软件开发过程中的行为和工作流程。下面我将详细分析每个主要规则文件的意义和作用。

## 1. 元规则（00-meta-rules.md）

**主要意义和作用**：
- 定义了AI助手的操作模式选择逻辑，确保AI在不同任务类型下采用适当的工作流程
- 建立了规则文件的处理顺序和优先级
- 提供了模式切换的决策机制

**具体规则内容**：
- 定义了三种主要操作模式：规划（PLANNING）、实现（IMPLEMENTATION）和调试（DEBUGGING）
- 提供了模式选择的层级决策逻辑：
  1. 优先考虑用户的显式命令（如`FOCUS = PLANNING`）
  2. 如无显式命令，则根据任务意图推断模式
  3. 考虑AI助手的内部状态，处理可能的冲突

**在AI编程中的效果**：
- 使AI能够根据任务性质自动切换工作模式，提供更专注和相关的帮助
- 减少了上下文切换的混乱，使AI能够保持一致的思维框架
- 确保AI在处理复杂项目时能够应用正确的工作流程和最佳实践

## 2. 内存系统规则（01-memory.md）

**主要意义和作用**：
- 定义了项目的"内存银行"结构，为AI提供持久化的项目上下文
- 建立了文档之间的层级关系和依赖关系
- 规定了内存文件的更新时机和流程

**具体规则内容**：
- 定义了7个核心文件：
  1. `product_requirement_docs.md`：项目需求和目标
  2. `architecture.md`：系统架构和组件关系
  3. `technical.md`：技术栈和关键决策
  4. `tasks_plan.md`：任务积压和项目进度
  5. `active_context.md`：当前开发状态
  6. `error-documentation.md`：错误文档
  7. `lessons-learned.md`：经验教训
- 规定了两种工作流程（PLAN模式和ACT模式）下的内存文件使用方式
- 定义了内存文件更新的时机和流程

**在AI编程中的效果**：
- 使AI能够保持长期项目上下文，避免重复解释项目背景
- 提供了结构化的知识库，使AI能够快速获取相关信息
- 确保AI的建议和代码与项目的架构、技术栈和最佳实践保持一致
- 通过记录错误和经验教训，使AI能够避免重复犯错

## 3. 一般原则规则（06-rules_v1.md）

**主要意义和作用**：
- 定义了AI助手在所有模式下都应遵循的基本原则和最佳实践
- 建立了信息收集和上下文整合的标准流程
- 规定了软件工程的基本原则

**具体规则内容（不同规则集的差异）**：

**轻量级规则集（light-spec）**：
- 简洁明了，专注于核心原则
- 强调"清晰优先"和"上下文为关键"
- 提供了软件工程基础原则的简明概述

**中等规则集（medium-spec）**：
- 在轻量级基础上增加了更多细节
- 更详细地描述了信息收集和上下文整合的流程
- 提供了更具体的软件工程原则指导

**重量级规则集（heavy-spec）**：
- 最详细和规范的版本
- 提供了全面的信息收集和资源使用指南
- 详细阐述了软件工程原则的各个方面
- 包含了更多具体的安全、性能和文档指导

**在AI编程中的效果**：
- 确保AI在所有交互中保持一致的高质量标准
- 使AI能够主动收集和整合项目上下文，提供更相关的建议
- 指导AI生成符合软件工程最佳实践的代码
- 根据项目的复杂性和需求，提供不同详细程度的指导

## 4. 规划工作流规则（01-plan_v1.md）

**主要意义和作用**：
- 定义了AI在规划模式下的工作流程和最佳实践
- 指导AI如何分析需求、设计解决方案和创建实施计划
- 确保规划过程考虑项目上下文和约束

**具体规则内容（不同规则集的差异）**：

**轻量级规则集**：
- 简化为三个主要步骤：理解与上下文化、设计与证明、详细计划
- 强调关键点，省略详细步骤

**中等规则集**：
- 四个主要步骤：澄清需求与上下文、开发与证明解决方案、创建详细实施计划、评估影响与请求验证
- 每个步骤都有更详细的指导

**重量级规则集**：
- 最详细的版本，包含五个主要步骤，每个步骤都有详细的子步骤和检查点
- 增加了内存影响评估和验证请求步骤

**在AI编程中的效果**：
- 指导AI进行系统化的需求分析和解决方案设计
- 确保AI的规划考虑项目的架构、技术约束和最佳实践
- 使AI能够创建详细、可行的实施计划，减少实施阶段的不确定性
- 促使AI主动考虑计划对项目内存文件的影响，保持文档的一致性

## 5. 实现工作流规则（01-code_v1.md）

**主要意义和作用**：
- 定义了AI在实现模式下的工作流程和最佳实践
- 指导AI如何根据规划执行代码实现
- 确保实现过程遵循项目标准和最佳实践

**具体规则内容（不同规则集的差异）**：

**轻量级规则集**：
- 简化为两个主要步骤：准备与验证、实现与迭代
- 强调关键点，省略详细步骤

**中等规则集**：
- 五个主要步骤：确认计划与上下文、增量执行计划步骤、开发测试、文档代码、报告完成与提出更新
- 每个步骤都有更详细的指导

**重量级规则集**：
- 最详细的版本，包含多个主要步骤和详细的子步骤
- 增加了更多的验证点和检查项

**在AI编程中的效果**：
- 指导AI按照计划有条不紊地实现代码
- 确保AI的实现符合项目的架构、技术标准和最佳实践
- 促使AI在实现过程中进行自我验证，及时发现和解决问题
- 指导AI编写测试和文档，提高代码质量和可维护性

## 6. 调试工作流规则（01-debug_v1.md）

**主要意义和作用**：
- 定义了AI在调试模式下的工作流程和最佳实践
- 指导AI如何系统地诊断、修复和验证错误
- 确保调试过程考虑项目上下文和约束

**具体规则内容（中等规则集）**：
- 七个主要步骤：收集上下文与复现、在上下文中分析、假设与推理、识别原因与计划/验证修复、实施与验证修复、处理持久性、报告结果与提出更新
- 强调在项目上下文中理解和解决问题

**在AI编程中的效果**：
- 指导AI进行系统化的错误诊断和修复
- 确保AI的调试考虑项目的架构、技术约束和最佳实践
- 促使AI记录错误和解决方案，避免同样的问题再次发生
- 提高AI在复杂项目中解决问题的能力

## 规则集之间的差异与选择

**三种规则集的主要差异**：

1. **详细程度**：
   - **heavy-spec**：最详细，提供了全面的指导和检查点
   - **medium-spec**：平衡详细度和简洁性，提供了足够的指导但省略了一些细节
   - **light-spec**：最简洁，专注于核心原则和关键步骤

2. **规范性**：
   - **heavy-spec**：高度规范，提供了明确的步骤和检查项
   - **medium-spec**：中等规范，提供了结构化的工作流程但允许一定的灵活性
   - **light-spec**：较低规范，提供了原则性指导但允许更多的灵活性

3. **适用场景**：
   - **heavy-spec**：适合复杂项目或需要高度一致性的场景
   - **medium-spec**：适合中等复杂度的项目或平衡指导和灵活性的场景
   - **light-spec**：适合简单项目或高度依赖AI能力的场景

**规则集选择的考虑因素**：

1. **项目复杂性**：复杂项目可能需要更详细的规则集（heavy-spec）
2. **团队经验**：经验丰富的团队可能更适合使用简洁的规则集（light-spec）
3. **一致性需求**：需要高度一致性的项目可能更适合使用详细的规则集（heavy-spec）
4. **AI能力**：更强大的AI模型可能能够更好地利用简洁的规则集（light-spec）

## 规则系统的整体效果

Rulebook-AI的规则系统在AI编程中产生了以下关键效果：

1. **结构化工作流程**：
   - 为AI提供了清晰的工作流程，使其能够系统地处理不同类型的任务
   - 减少了混乱和遗漏，提高了工作效率和质量

2. **上下文感知**：
   - 通过内存系统，使AI能够保持长期项目上下文
   - 确保AI的建议和代码与项目的架构、技术栈和最佳实践保持一致

3. **最佳实践执行**：
   - 强制执行软件工程最佳实践，提高代码质量
   - 确保安全、性能、可维护性等关键方面得到充分考虑

4. **跨平台一致性**：
   - 在不同AI编码助手平台上提供一致的行为和工作流程
   - 减少了平台切换的成本和混乱

5. **自适应指导**：
   - 通过提供不同详细程度的规则集，适应不同复杂度的项目和团队需求
   - 在保持核心原则的同时，允许适当的灵活性

总的来说，Rulebook-AI的规则系统通过提供结构化的工作流程、持久化的项目上下文和软件工程最佳实践指导，显著提高了AI编码助手在软件开发过程中的效能和价值。它解决了AI编码助手在复杂项目中面临的一致性、上下文和最佳实践问题，使AI能够更有效地参与软件开发过程。


---

### 对话 4

> **👤 用户** (2025年05月11日 03:59)

那么假设已经集成到 Cursor 或者 cline 等 AI编程工具中，用户需要主动触发规则的执行吗？还是正常提需求，编程工具会自动根据对应的规则来完成执行？ 如果是自动请你帮我解释对应的原理是什么？

> **🤖 Augment** (2025年05月11日 03:59)

# Rulebook-AI 在 AI 编程工具中的集成与执行机制

在 Rulebook-AI 集成到 Cursor、CLINE 等 AI 编程工具后，用户通常**不需要主动触发规则的执行**。系统设计为自动化运行，让用户能够正常提出需求，而编程工具会自动根据对应的规则来完成执行。下面我将详细解释这种自动执行的原理和机制。

## 自动规则执行的原理

Rulebook-AI 的自动执行原理基于以下几个关键机制：

### 1. 规则加载机制

每个支持的 AI 编程工具都有特定的规则加载机制，Rulebook-AI 利用这些机制在工具启动或项目打开时自动加载规则：

- **Cursor**：使用 `.cursor/rules/` 目录中的 `.mdc` 文件
- **CLINE**：使用 `.clinerules` 目录中的规则文件
- **RooCode**：使用 `.roo/rules/` 和 `.roo/rules-{modeSlug}/` 目录
- **Windsurf**：使用 `.windsurfrules` 文件
- **Github Copilot**：使用 `.github/copilot-instructions.md` 文件

当用户打开项目时，AI 编程工具会自动读取这些规则文件，并将其内容作为 AI 模型的系统提示（system prompt）或上下文的一部分。

### 2. 元规则的模式识别机制

`00-meta-rules.md` 文件中定义了自动模式识别和切换的逻辑：

```
1. 检查用户的最新请求是否包含显式指令（如 `FOCUS = PLANNING`）
2. 如果没有显式指令，分析用户的当前请求以确定主要任务意图：
   - 是关于高级设计、分析、创建计划、探索解决方案？-> FOCUS = PLANNING
   - 是关于编写代码、实现特定步骤、进行直接修改？-> FOCUS = IMPLEMENTATION
   - 是关于修复报告的错误、诊断意外行为、分析失败？-> FOCUS = DEBUGGING
```

这种机制使 AI 能够自动分析用户的请求，确定适当的操作模式，并应用相应的规则集，无需用户明确指定。

### 3. 内存文件自动访问

规则系统指导 AI 在处理任务前自动检查相关的内存文件：

```
在进行重要工作（规划、编码、调试）之前：
1. 理解特定任务
2. 主动检查内存库的相关部分（如 architecture.md、technical.md 等）
3. 分析受影响区域的现有代码
```

这种机制确保 AI 在每次交互中都能自动获取和整合相关的项目上下文，无需用户明确指示。

### 4. 规则同步机制

`manage_rules.py` 脚本的 `sync` 命令确保规则在不同平台之间保持一致：

```bash
python src/manage_rules.py sync ~/path/to/your/project
```

这个命令会自动将 `project_rules/` 目录中的规则文件转换为各个平台特定的格式，并放置在正确的位置，确保跨平台一致性。

## 实际工作流程示例

以下是 Rulebook-AI 在实际使用中的自动执行流程示例：

### 场景：用户请求实现新功能

1. **用户输入**：
   ```
   "我需要实现一个用户注册功能，包括表单验证和数据库存储"
   ```

2. **自动模式识别**：
   - AI 分析请求，识别为实现任务
   - 自动设置 `FOCUS = IMPLEMENTATION`

3. **自动上下文收集**：
   - AI 自动检查相关内存文件：
     - `architecture.md` 了解系统架构和组件关系
     - `technical.md` 了解技术栈和设计模式
     - `tasks_plan.md` 了解相关任务状态
     - `active_context.md` 了解当前开发状态

4. **应用实现工作流规则**：
   - 确认计划与上下文
   - 增量执行计划步骤
   - 开发测试
   - 文档代码
   - 报告完成与提出更新

5. **响应用户**：
   - 提供符合项目架构和技术标准的实现方案
   - 包括必要的测试和文档

整个过程中，用户只需提出需求，无需明确指示 AI 使用哪些规则或检查哪些文件。

## 显式模式切换（可选但非必需）

虽然系统设计为自动运行，但用户仍可以通过显式命令切换模式：

```
"FOCUS = PLANNING 我想重新设计用户认证系统"
```

这种显式命令会覆盖自动模式识别，强制 AI 使用指定的工作流程。这在某些情况下很有用，例如当用户想要进行规划但请求可能被误解为实现任务时。

## 技术实现原理

从技术角度看，自动规则执行基于以下原理：

### 1. LLM 上下文窗口利用

AI 编程工具将规则文件的内容注入到 LLM（大型语言模型）的上下文窗口中，作为系统提示或指令的一部分。这些规则成为 AI 响应生成过程的一部分，指导其行为和输出。

### 2. 提示工程（Prompt Engineering）

规则文件使用精心设计的提示工程技术，包括：
- 明确的指令和优先级
- 结构化的工作流程定义
- 条件逻辑（如模式选择规则）
- 自我监督机制（如验证步骤）

这些技术使 AI 能够根据规则自动调整其行为，而无需额外的外部控制逻辑。

### 3. 文件系统集成

AI 编程工具通常有权限访问项目的文件系统，这使得：
- 规则文件可以从特定位置自动加载
- 内存文件可以被 AI 自动访问和引用
- AI 可以根据规则更新或建议更新内存文件

### 4. 平台特定的规则适配

`manage_rules.py` 脚本通过以下函数处理平台特定的规则格式转换：
- `copy_and_number_files`：处理 Cursor 和 CLINE 规则
- `copy_and_restructure_roocode`：处理 RooCode 规则
- `concatenate_ordered_files`：处理 Windsurf 和 Github Copilot 规则

这确保了相同的规则逻辑可以在不同平台上一致执行，尽管它们的规则格式不同。

## 总结

Rulebook-AI 在 AI 编程工具中的集成是自动化的，用户只需正常提出需求，无需主动触发规则执行。系统通过规则加载机制、自动模式识别、内存文件自动访问和规则同步机制，确保 AI 在处理用户请求时自动应用适当的规则和上下文。

这种自动化设计大大提高了用户体验，使用户能够专注于表达需求和解决问题，而不必担心规则的执行细节。同时，它也确保了 AI 的响应始终符合项目的架构、技术标准和最佳实践，提高了开发效率和代码质量。


---

### 对话 5

> **👤 用户** (2025年05月11日 04:05)

那么既然是自动执行，AI的自动模式识别，识别出了什么模式，以及应用了哪些规则，其实都是黑盒化的，能否白盒化，让用户指导现在采用的是什么模式，使用了什么规则，如果是要做到这点，思考该如何实现 

> **🤖 Augment** (2025年05月11日 04:05)

# 将 Rulebook-AI 的模式识别和规则应用白盒化

您提出了一个很好的问题。确实，AI 的自动模式识别和规则应用过程在当前实现中是黑盒化的，用户无法直接了解 AI 当前采用的模式和应用的规则。将这个过程白盒化可以提高透明度、可控性和可预测性，让用户更好地理解和指导 AI 的行为。下面我将探讨如何实现这种白盒化。

## 白盒化的实现方案

### 1. 模式和规则应用的显式声明

**实现方式**：修改规则系统，要求 AI 在每次响应开始时明确声明当前采用的模式和应用的规则。

**具体实现**：

```markdown
# 在 00-meta-rules.md 中添加以下指令

在每次响应的开始，你必须明确声明：
1. 当前采用的操作模式（PLANNING、IMPLEMENTATION 或 DEBUGGING）
2. 应用的主要规则文件（如 general-principles、planning-workflow 等）
3. 参考的主要内存文件（如 architecture.md、technical.md 等）

使用以下格式：

---
**当前模式**: [模式名称]
**应用规则**: [主要规则文件列表]
**参考内存**: [主要内存文件列表]
---

此声明应简洁明了，不超过 5 行，并使用代码块或其他视觉区分方式与主要响应分开。
```

**示例输出**：
```
---
**当前模式**: IMPLEMENTATION
**应用规则**: general-principles, implementation-workflow
**参考内存**: architecture.md, technical.md, active_context.md
---

基于您的请求，我将实现用户注册功能。首先，让我分析现有的架构和技术约束...
```

### 2. 模式切换的显式通知

**实现方式**：当 AI 检测到需要切换模式时，要求其明确通知用户并请求确认。

**具体实现**：

```markdown
# 在 00-meta-rules.md 中添加以下指令

当你检测到需要从一种模式切换到另一种模式时（例如，从 IMPLEMENTATION 切换到 DEBUGGING），你必须：

1. 暂停当前工作流
2. 明确通知用户建议的模式切换
3. 解释切换的原因
4. 请求用户确认或拒绝切换

使用以下格式：

---
**模式切换建议**: [当前模式] → [建议模式]
**切换原因**: [简要解释]
**请确认**: 您希望我继续使用 [当前模式] 还是切换到 [建议模式]？
---

只有在用户确认后才执行模式切换。
```

**示例输出**：
```
---
**模式切换建议**: IMPLEMENTATION → DEBUGGING
**切换原因**: 在实现过程中发现了一个可能的错误，需要进行调试
**请确认**: 您希望我继续使用 IMPLEMENTATION 模式还是切换到 DEBUGGING 模式？
---
```

### 3. 规则应用的详细日志（可选展开）

**实现方式**：提供一个可选的详细日志，记录 AI 在处理请求过程中应用的具体规则和决策点。

**具体实现**：

```markdown
# 在 06-rules_v1.md 中添加以下指令

在每次响应的末尾，提供一个折叠的详细日志部分，记录你在处理请求过程中应用的具体规则和决策点。

使用以下格式：

<details>
<summary>**规则应用日志** (点击展开)</summary>

1. **模式识别**:
   - 用户请求分析: [简要分析]
   - 应用规则: [00-meta-rules.md 中的相关规则]
   - 决策结果: [选择的模式]

2. **内存文件访问**:
   - 访问的文件: [文件列表]
   - 提取的关键信息: [简要概述]
   - 应用规则: [01-memory.md 中的相关规则]

3. **工作流应用**:
   - 应用的工作流: [工作流名称]
   - 执行的步骤: [步骤列表]
   - 应用规则: [相关工作流规则文件中的规则]

4. **决策点**:
   - [决策点 1]: [决策描述] - 基于 [相关规则]
   - [决策点 2]: [决策描述] - 基于 [相关规则]
   - ...
</details>
```

**示例输出**：
```
<details>
<summary>**规则应用日志** (点击展开)</summary>

1. **模式识别**:
   - 用户请求分析: "实现用户注册功能" 表明这是一个实现任务
   - 应用规则: 00-meta-rules.md 第 18-21 行的任务意图推断规则
   - 决策结果: IMPLEMENTATION 模式

2. **内存文件访问**:
   - 访问的文件: architecture.md, technical.md, active_context.md
   - 提取的关键信息: 用户认证组件位于 auth/ 目录，使用 JWT 标准，数据库使用 PostgreSQL
   - 应用规则: 01-memory.md 第 42-76 行的核心文件访问规则

3. **工作流应用**:
   - 应用的工作流: implementation-workflow
   - 执行的步骤: 确认计划与上下文, 准备与验证步骤, 实现代码
   - 应用规则: 01-code_v1.md 第 10-16 行的实现步骤规则

4. **决策点**:
   - 表单验证方法: 选择使用项目现有的验证库 - 基于 technical.md 中的技术栈规范
   - 错误处理策略: 实现统一错误处理 - 基于 06-rules_v1.md 第 58-61 行的健壮性原则
   - 测试策略: 编写单元测试和集成测试 - 基于 01-code_v1.md 第 17 行的测试开发规则
</details>
```

### 4. 用户模式和规则控制面板

**实现方式**：在 AI 编程工具的 UI 中添加一个控制面板，显示当前模式和应用的规则，并允许用户手动调整。

**具体实现**：

1. **修改 `manage_rules.py` 脚本**，添加生成控制面板配置的功能：

```python
def generate_control_panel_config(target_repo_path):
    """生成控制面板配置文件"""
    config = {
        "modes": ["PLANNING", "IMPLEMENTATION", "DEBUGGING"],
        "rule_sets": {
            "general": ["06-rules_v1.md"],
            "planning": ["01-plan_v1.md"],
            "implementation": ["01-code_v1.md"],
            "debugging": ["01-debug_v1.md"]
        },
        "memory_files": [
            "product_requirement_docs.md",
            "architecture.md",
            "technical.md",
            "tasks_plan.md",
            "active_context.md",
            "error-documentation.md",
            "lessons-learned.md"
        ]
    }
    
    config_path = os.path.join(target_repo_path, ".rulebook-control-panel.json")
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"Control panel configuration generated at {config_path}")
```

2. **为各平台创建控制面板集成**：

- **Cursor**：创建一个 VSCode 扩展，读取配置文件并显示控制面板
- **CLINE**：添加命令行界面，显示和控制当前模式和规则
- **RooCode/Windsurf**：集成到其 UI 中的侧边栏或状态栏

3. **控制面板功能**：

- 显示当前模式（PLANNING/IMPLEMENTATION/DEBUGGING）
- 显示当前应用的规则文件
- 显示最近访问的内存文件
- 允许用户手动切换模式
- 允许用户启用/禁用特定规则
- 提供规则应用日志的实时查看

### 5. 规则应用标记和注释

**实现方式**：修改 AI 的输出格式，使用特殊标记或注释来指示特定规则的应用。

**具体实现**：

```markdown
# 在 06-rules_v1.md 中添加以下指令

当你应用特定规则生成内容时，使用以下注释格式标记该内容：

<!-- RULE: [规则文件名]:[规则部分] -->
内容
<!-- /RULE -->

例如：

<!-- RULE: 06-rules_v1:Robustness -->
// 输入验证
if (!input || typeof input !== 'string') {
  throw new Error('Invalid input');
}
<!-- /RULE -->

这些注释应该在最终输出中保留，但应尽量不影响内容的可读性。
```

**示例输出**：
```javascript
// 用户注册函数
function registerUser(userData) {
  <!-- RULE: 06-rules_v1:Robustness -->
  // 输入验证
  if (!userData || typeof userData !== 'object') {
    throw new Error('Invalid user data');
  }
  
  if (!userData.email || !isValidEmail(userData.email)) {
    throw new Error('Invalid email address');
  }
  <!-- /RULE -->
  
  <!-- RULE: 01-code_v1:ImplementStep -->
  // 数据库存储
  try {
    const user = new User(userData);
    return user.save();
  } catch (error) {
    logger.error('User registration failed', error);
    throw new Error('Registration failed');
  }
  <!-- /RULE -->
}
```

## 综合实现方案

为了全面实现 Rulebook-AI 的白盒化，我建议采用以下综合方案：

### 1. 核心规则文件修改

1. **修改 `00-meta-rules.md`**：
   - 添加模式和规则应用的显式声明要求
   - 添加模式切换的显式通知要求

2. **修改 `06-rules_v1.md`**：
   - 添加规则应用的详细日志要求
   - 添加规则应用标记和注释要求

### 2. 工具和脚本增强

1. **增强 `manage_rules.py` 脚本**：
   - 添加生成控制面板配置的功能
   - 添加规则应用日志记录的支持

2. **创建新的辅助工具**：
   - `rulebook-monitor.py`：实时监控和显示 AI 的模式和规则应用
   - `rulebook-config.py`：允许用户配置模式和规则的优先级

### 3. 平台集成

1. **为各 AI 编程工具创建插件或扩展**：
   - Cursor 扩展：显示控制面板和规则应用日志
   - CLINE 命令：提供模式和规则控制的命令行界面
   - RooCode/Windsurf 集成：在 UI 中显示当前模式和规则

### 4. 文档和用户指南

1. **创建白盒化用户指南**：
   - 解释如何解读模式和规则声明
   - 说明如何使用控制面板
   - 提供常见模式切换场景的示例

2. **更新项目 README**：
   - 添加白盒化功能的说明
   - 提供快速入门指南

## 实现示例：控制面板 UI

以下是一个简化的控制面板 UI 设计，可以集成到 AI 编程工具中：

```
+-----------------------------------------------+
| Rulebook-AI Control Panel                     |
+-----------------------------------------------+
| Current Mode: [IMPLEMENTATION] ▼              |
|   ○ PLANNING                                  |
|   ● IMPLEMENTATION                            |
|   ○ DEBUGGING                                 |
+-----------------------------------------------+
| Applied Rules:                                |
|   ☑ general-principles (06-rules_v1.md)       |
|   ☐ planning-workflow (01-plan_v1.md)         |
|   ☑ implementation-workflow (01-code_v1.md)   |
|   ☐ debugging-workflow (01-debug_v1.md)       |
+-----------------------------------------------+
| Memory Files:                                 |
|   ☑ architecture.md                           |
|   ☑ technical.md                              |
|   ☑ active_context.md                         |
|   ☐ Show all files...                         |
+-----------------------------------------------+
| [View Rule Application Log]                   |
+-----------------------------------------------+
```

## 白盒化的优势与挑战

### 优势

1. **透明度**：用户清楚了解 AI 当前的操作模式和应用的规则
2. **可控性**：用户可以手动调整模式和规则，更好地指导 AI 的行为
3. **可预测性**：用户可以预测 AI 的行为和决策过程
4. **学习机会**：用户可以通过观察规则应用过程，学习软件工程最佳实践
5. **调试能力**：当 AI 行为不符合预期时，用户可以更容易地找出原因

### 挑战

1. **复杂性增加**：白盒化可能增加系统的复杂性和用户界面的复杂度
2. **性能开销**：详细记录规则应用过程可能增加处理时间和令牌使用量
3. **用户认知负担**：过多的信息可能增加用户的认知负担
4. **实现难度**：需要修改多个组件和平台集成
5. **维护成本**：白盒化功能需要随着规则系统的更新而维护

## 结论

将 Rulebook-AI 的模式识别和规则应用白盒化是完全可行的，通过修改核心规则文件、增强工具和脚本、创建平台集成以及提供文档和用户指南，可以实现这一目标。白盒化将提高系统的透明度、可控性和可预测性，使用户能够更好地理解和指导 AI 的行为。

虽然实现白盒化面临一些挑战，如复杂性增加和性能开销，但这些挑战可以通过精心的设计和实现来克服。最终，白盒化将使 Rulebook-AI 成为一个更加强大和用户友好的工具，为 AI 辅助编程提供更好的体验。


---

