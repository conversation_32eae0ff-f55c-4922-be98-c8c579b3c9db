"""
Free Augment - Identity Reset Tools for VS Code Augment Code Plugin

This package provides tools to reset the identity of VS Code Augment Code plugin,
allowing users to use the plugin with a new email address.

Main components:
- reset_augment_identity: Main entry point for the identity reset process
- identity_tools: Specific utilities for modifying plugin data
- utils: Common utilities for path management and device code generation

Usage:
    from augment.free_augment import reset_identity
    reset_identity()

Or run the main script:
    python -m augment.free_augment.reset_augment_identity
"""

from .utils import (
    get_home_dir, 
    get_app_data_dir, 
    get_storage_path, 
    get_db_path, 
    get_machine_id_path,
    get_workspace_storage_path,
    generate_machine_id, 
    generate_device_id
)

from .identity_tools import (
    modify_telemetry_ids,
    clean_augment_data,
    clean_workspace_storage
)

def reset_identity():
    """
    Main function to reset Augment Code plugin identity.
    This is a convenience function that runs the complete reset process.
    """
    from .reset_augment_identity import main
    main()

__all__ = [
    # Main function
    'reset_identity',
    # Utils functions
    'get_home_dir',
    'get_app_data_dir',
    'get_storage_path',
    'get_db_path',
    'get_machine_id_path',
    'get_workspace_storage_path',
    'generate_machine_id',
    'generate_device_id',
    # Identity tools functions
    'modify_telemetry_ids',
    'clean_augment_data',
    'clean_workspace_storage',
]
