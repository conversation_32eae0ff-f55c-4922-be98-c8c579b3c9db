"""
Augment package containing utilities for VS Code Augment Code plugin management.

This package includes:
- utils: Common utilities for path management and device code generation
- augutils: Specific utilities for modifying Augment Code plugin data
- extract_augment_chat: Tool for extracting Augment Code chat history
- index: Main entry point for the free-augmentcode functionality
"""

from .utils import (
    get_home_dir, 
    get_app_data_dir, 
    get_storage_path, 
    get_db_path, 
    get_machine_id_path,
    get_workspace_storage_path,
    generate_machine_id, 
    generate_device_id
)

from .augutils import (
    modify_telemetry_ids,
    clean_augment_data,
    clean_workspace_storage
)

__all__ = [
    # Utils functions
    'get_home_dir',
    'get_app_data_dir',
    'get_storage_path',
    'get_db_path',
    'get_machine_id_path',
    'get_workspace_storage_path',
    'generate_machine_id',
    'generate_device_id',
    # Augutils functions
    'modify_telemetry_ids',
    'clean_augment_data',
    'clean_workspace_storage',
]
