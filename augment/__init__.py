"""
Augment package containing utilities for VS Code Augment Code plugin management.

This package includes:
- extract_augment_chat: Tool for extracting Augment Code chat history
- free_augment: Independent package for resetting Augment Code plugin identity

Main components:
1. extract_augment_chat.py - Standalone tool for extracting chat history
2. free_augment/ - Complete identity reset solution with utilities

Usage:
    # For chat extraction
    from augment.extract_augment_chat import find_augment_projects

    # For identity reset
    from augment.free_augment import reset_identity
    reset_identity()
"""

# Import the main functionality from free_augment subpackage
from .free_augment import reset_identity

__all__ = [
    'reset_identity',
]
