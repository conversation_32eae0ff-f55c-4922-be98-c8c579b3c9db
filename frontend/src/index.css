body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Background is now managed by Material UI theme */
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
  background-color: #2D2D2D;
  color: #E6E6E6;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 85%;
}

pre {
  background-color: #2D2D2D;
  color: #E6E6E6;
  padding: 16px;
  border-radius: 6px;
  overflow: auto;
}

a {
  color: #6E2CF4;
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: #202020;
}

::-webkit-scrollbar-thumb {
  background: #4A4A4A;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: #5F5F5F;
} 