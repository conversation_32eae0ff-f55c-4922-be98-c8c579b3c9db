#!/usr/bin/env python3
"""
Simple API server to serve Cursor chat data for the web interface.
"""

import json
import uuid
import logging
import datetime
import os
import platform
import sqlite3
import argparse
import pathlib
from collections import defaultdict
from typing import Dict, Any, Iterable
from pathlib import Path
from flask import Flask, Response, jsonify, send_from_directory, request
from flask_cors import CORS

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Enable more detailed logging for debugging Cursor storage locations
DETAILED_LOGGING = True

app = Flask(__name__, static_folder='frontend/build')
CORS(app)

################################################################################
# Cursor storage roots
################################################################################
def cursor_root() -> pathlib.Path:
    h = pathlib.Path.home()
    s = platform.system()
    if s == "Darwin":   return h / "Library" / "Application Support" / "Cursor"
    if s == "Windows":  return h / "AppData" / "Roaming" / "Cursor"
    if s == "Linux":    return h / ".config" / "Cursor"
    raise RuntimeError(f"Unsupported OS: {s}")

################################################################################
# Helpers
################################################################################
def j(cur: sqlite3.Cursor, table: str, key: str):
    cur.execute(f"SELECT value FROM {table} WHERE key=?", (key,))
    row = cur.fetchone()
    if row:
        try:    return json.loads(row[0])
        except Exception as e:
            logger.debug(f"Failed to parse JSON for {key}: {e}")
    return None

def iter_bubbles_from_disk_kv(db: pathlib.Path) -> Iterable[tuple[str,str,str,str]]:
    """Yield (composerId, role, text, db_path) from cursorDiskKV table."""
    if DETAILED_LOGGING:
        logger.info(f"Extracting bubbles from database: {db}")

    try:
        con = sqlite3.connect(f"file:{db}?mode=ro", uri=True)
        cur = con.cursor()

        # Check if table exists
        cur.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cur.fetchall()]

        if DETAILED_LOGGING:
            logger.info(f"Tables in database {db.name}: {tables}")

        if "cursorDiskKV" not in tables:
            if DETAILED_LOGGING:
                logger.warning(f"No cursorDiskKV table found in {db}")
            con.close()
            return

        # Check for key patterns in cursorDiskKV
        if DETAILED_LOGGING:
            try:
                cur.execute("SELECT DISTINCT substr(key, 1, instr(key, ':') - 1) FROM cursorDiskKV")
                key_prefixes = [row[0] for row in cur.fetchall()]
                logger.info(f"Key prefixes in cursorDiskKV: {key_prefixes}")

                # Count bubbleId entries
                cur.execute("SELECT COUNT(*) FROM cursorDiskKV WHERE key LIKE 'bubbleId:%'")
                bubble_count = cur.fetchone()[0]
                logger.info(f"Found {bubble_count} bubbleId entries in {db.name}")

                # 获取所有不同的composerId
                cur.execute("""
                    SELECT DISTINCT
                        substr(kv1.key, instr(kv1.key, ':') + 1, instr(substr(kv1.key, instr(kv1.key, ':') + 1), ':') - 1) AS composerId
                    FROM cursorDiskKV kv1
                    WHERE kv1.key LIKE 'bubbleId:%'
                """)
                composer_ids = [row[0] for row in cur.fetchall()]
                logger.info(f"从bubbleId条目中找到 {len(composer_ids)} 个不同的composerId: {composer_ids[:5]}{'...' if len(composer_ids) > 5 else ''}")
            except Exception as e:
                logger.error(f"Error analyzing cursorDiskKV keys: {e}")

        # Get all bubbleId entries
        cur.execute("SELECT key, value FROM cursorDiskKV WHERE key LIKE 'bubbleId:%'")
    except sqlite3.DatabaseError as e:
        logger.error(f"Database error with {db}: {e}")
        return

    db_path_str = str(db)
    message_count = 0
    composer_message_counts = {}  # 跟踪每个composerId的消息数量

    for k, v in cur.fetchall():
        try:
            if v is None:
                continue

            b = json.loads(v)

            # Log the structure of the first bubble for debugging
            if message_count == 0 and DETAILED_LOGGING:
                logger.info(f"Sample bubble structure: {json.dumps({k: list(b.keys())})}")
        except Exception as e:
            logger.debug(f"Failed to parse bubble JSON for key {k}: {e}")
            continue

        txt = (b.get("text") or b.get("richText") or "").strip()
        if not txt:
            continue

        role = "user" if b.get("type") == 1 else "assistant"

        # Extract composerId from key (format is bubbleId:composerId:bubbleId)
        try:
            key_parts = k.split(":")
            if len(key_parts) >= 2:
                composerId = key_parts[1]
            else:
                if DETAILED_LOGGING:
                    logger.warning(f"Unexpected key format: {k}")
                composerId = "unknown"
        except Exception as e:
            logger.error(f"Error parsing key {k}: {e}")
            composerId = "unknown"

        # 更新此composerId的消息计数
        if composerId in composer_message_counts:
            composer_message_counts[composerId] += 1
        else:
            composer_message_counts[composerId] = 1

        message_count += 1
        yield composerId, role, txt, db_path_str

    if DETAILED_LOGGING:
        logger.info(f"Extracted {message_count} messages from {db.name}")
        logger.info(f"消息分布在 {len(composer_message_counts)} 个composerId中")
        # 显示每个composerId的消息数量
        for composer_id, count in sorted(composer_message_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
            logger.info(f"  - composerId {composer_id[:8]}: {count} 条消息")

    con.close()

def iter_chat_from_item_table(db: pathlib.Path) -> Iterable[tuple[str,str,str,str]]:
    """Yield (composerId, role, text, db_path) from ItemTable."""
    message_count = 0
    tab_ids = set()  # 跟踪找到的tabId
    composer_ids = set()  # 跟踪找到的composerId
    ai_service_ids = set()  # 跟踪找到的aiService ID

    try:
        con = sqlite3.connect(f"file:{db}?mode=ro", uri=True)
        cur = con.cursor()

        # Try to get chat data from workbench.panel.aichat.view.aichat.chatdata
        chat_data = j(cur, "ItemTable", "workbench.panel.aichat.view.aichat.chatdata")
        if chat_data and "tabs" in chat_data:
            tabs_count = len(chat_data.get("tabs", []))
            if DETAILED_LOGGING:
                logger.info(f"在 {db.name} 的 ItemTable 中找到 {tabs_count} 个聊天标签页")

            for tab in chat_data.get("tabs", []):
                tab_id = tab.get("tabId", "unknown")
                tab_ids.add(tab_id)
                bubble_count = len(tab.get("bubbles", []))
                tab_message_count = 0

                for bubble in tab.get("bubbles", []):
                    bubble_type = bubble.get("type")
                    if not bubble_type:
                        continue

                    # Extract text from various possible fields
                    text = ""
                    if "text" in bubble:
                        text = bubble["text"]
                    elif "content" in bubble:
                        text = bubble["content"]

                    if text and isinstance(text, str):
                        role = "user" if bubble_type == "user" else "assistant"
                        message_count += 1
                        tab_message_count += 1
                        yield tab_id, role, text, str(db)

                if tab_message_count > 0 and DETAILED_LOGGING:
                    logger.info(f"  - 从标签页 {tab_id[:8]} 提取了 {tab_message_count}/{bubble_count} 条消息")

        # Check for composer data
        composer_data = j(cur, "ItemTable", "composer.composerData")
        if composer_data:
            composers_count = len(composer_data.get("allComposers", []))
            if DETAILED_LOGGING:
                logger.info(f"在 {db.name} 的 ItemTable 中找到 {composers_count} 个composer")

            for comp in composer_data.get("allComposers", []):
                comp_id = comp.get("composerId", "unknown")
                composer_ids.add(comp_id)
                messages = comp.get("messages", [])
                comp_message_count = 0

                for msg in messages:
                    role = msg.get("role", "unknown")
                    content = msg.get("content", "")
                    if content:
                        message_count += 1
                        comp_message_count += 1
                        yield comp_id, role, content, str(db)

                if comp_message_count > 0 and DETAILED_LOGGING:
                    logger.info(f"  - 从composer {comp_id[:8]} 提取了 {comp_message_count}/{len(messages)} 条消息")

        # Also check for aiService entries
        for key_prefix in ["aiService.prompts", "aiService.generations"]:
            try:
                cur.execute("SELECT key, value FROM ItemTable WHERE key LIKE ?", (f"{key_prefix}%",))
                rows = cur.fetchall()
                if rows and DETAILED_LOGGING:
                    logger.info(f"在 {db.name} 的 ItemTable 中找到 {len(rows)} 个 {key_prefix} 条目")

                for k, v in rows:
                    try:
                        data = json.loads(v)
                        if isinstance(data, list):
                            for item in data:
                                if "id" in item and "text" in item:
                                    item_id = item.get("id", "unknown")
                                    ai_service_ids.add(item_id)
                                    role = "user" if "prompts" in key_prefix else "assistant"
                                    message_count += 1
                                    yield item_id, role, item.get("text", ""), str(db)
                    except json.JSONDecodeError:
                        continue
            except sqlite3.Error as e:
                if DETAILED_LOGGING:
                    logger.debug(f"查询 {key_prefix} 时出错: {e}")
                continue

    except sqlite3.DatabaseError as e:
        logger.debug(f"Database error in ItemTable with {db}: {e}")
        return
    finally:
        if 'con' in locals():
            con.close()

    # 汇总日志
    if message_count > 0 and DETAILED_LOGGING:
        logger.info(f"从 {db.name} 的 ItemTable 总共提取了 {message_count} 条消息")
        logger.info(f"  - 标签页IDs: {len(tab_ids)} 个")
        logger.info(f"  - ComposerIDs: {len(composer_ids)} 个")
        logger.info(f"  - AIService IDs: {len(ai_service_ids)} 个")

def iter_composer_data(db: pathlib.Path) -> Iterable[tuple[str,dict,str]]:
    """Yield (composerId, composerData, db_path) from cursorDiskKV table."""
    composer_count = 0
    composer_with_conversation_count = 0

    try:
        con = sqlite3.connect(f"file:{db}?mode=ro", uri=True)
        cur = con.cursor()
        # Check if table exists
        cur.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='cursorDiskKV'")
        if not cur.fetchone():
            if DETAILED_LOGGING:
                logger.info(f"在 {db.name} 中没有找到 cursorDiskKV 表")
            con.close()
            return

        # 计算 composerData 条目数量
        cur.execute("SELECT COUNT(*) FROM cursorDiskKV WHERE key LIKE 'composerData:%'")
        count = cur.fetchone()[0]
        if DETAILED_LOGGING:
            logger.info(f"在 {db.name} 中找到 {count} 个 composerData 条目")

        # 获取所有不同的 composerId
        cur.execute("SELECT DISTINCT substr(key, instr(key, ':') + 1) FROM cursorDiskKV WHERE key LIKE 'composerData:%'")
        composer_ids = [row[0] for row in cur.fetchall()]
        if DETAILED_LOGGING and composer_ids:
            logger.info(f"从 composerData 条目中找到 {len(composer_ids)} 个不同的 composerId: {composer_ids[:5]}{'...' if len(composer_ids) > 5 else ''}")

        cur.execute("SELECT key, value FROM cursorDiskKV WHERE key LIKE 'composerData:%'")
    except sqlite3.DatabaseError as e:
        logger.debug(f"Database error with {db}: {e}")
        return

    db_path_str = str(db)

    for k, v in cur.fetchall():
        try:
            if v is None:
                continue

            composer_data = json.loads(v)
            composer_id = k.split(":")[1]
            composer_count += 1

            # 检查是否有对话数据
            conversation = composer_data.get("conversation", [])
            if conversation:
                msg_count = len([msg for msg in conversation if msg.get("text") and isinstance(msg.get("text"), str)])
                if msg_count > 0:
                    composer_with_conversation_count += 1
                    if DETAILED_LOGGING:
                        logger.info(f"  - composerId {composer_id[:8]} 包含 {msg_count} 条有效消息")

            yield composer_id, composer_data, db_path_str

        except Exception as e:
            logger.debug(f"Failed to parse composer data for key {k}: {e}")
            continue

    if DETAILED_LOGGING and composer_count > 0:
        logger.info(f"处理了 {composer_count} 个 composer 数据，其中 {composer_with_conversation_count} 个包含有效对话")

    con.close()

################################################################################
# Workspace discovery
################################################################################
def workspaces(base: pathlib.Path):
    ws_root = base / "User" / "workspaceStorage"
    if not ws_root.exists():
        return
    for folder in ws_root.iterdir():
        db = folder / "state.vscdb"
        if db.exists():
            yield folder.name, db

def extract_project_name_from_path(root_path, debug=False):
    """
    Extract a project name from a path, skipping user directories.
    """
    if not root_path or root_path == '/':
        return "Root"

    path_parts = [p for p in root_path.split('/') if p]

    # Skip common user directory patterns
    project_name = None
    home_dir_patterns = ['Users', 'home']

    # Get current username for comparison
    current_username = os.path.basename(os.path.expanduser('~'))

    # Find user directory in path
    username_index = -1
    for i, part in enumerate(path_parts):
        if part in home_dir_patterns:
            username_index = i + 1
            break

    # If this is just /Users/<USER>'t use username as project
    if username_index >= 0 and username_index < len(path_parts) and path_parts[username_index] == current_username:
        if len(path_parts) <= username_index + 1:
            return "Home Directory"

    if username_index >= 0 and username_index + 1 < len(path_parts):
        # First try specific project directories we know about by name
        known_projects = ['genaisf', 'cursor-view', 'cursor', 'cursor-apps', 'universal-github', 'inquiry']

        # Look at the most specific/deepest part of the path first
        for i in range(len(path_parts)-1, username_index, -1):
            if path_parts[i] in known_projects:
                project_name = path_parts[i]
                if debug:
                    logger.debug(f"Found known project name from specific list: {project_name}")
                break

        # If no known project found, use the last part of the path as it's likely the project directory
        if not project_name and len(path_parts) > username_index + 1:
            # Check if we have a structure like /Users/<USER>/Documents/codebase/project_name
            if 'Documents' in path_parts and 'codebase' in path_parts:
                # 获取codebase的索引位置
                codebase_index = path_parts.index('codebase')

                # If there's a path component after 'codebase', use that as the project name
                if codebase_index + 1 < len(path_parts):
                    project_name = path_parts[codebase_index + 1]
                    if debug:
                        logger.debug(f"Found project name in Documents/codebase structure: {project_name}")

            # If no specific structure found, use the last component of the path
            if not project_name:
                project_name = path_parts[-1]
                if debug:
                    logger.debug(f"Using last path component as project name: {project_name}")

        # Skip username as project name
        if project_name == current_username:
            project_name = 'Home Directory'
            if debug:
                logger.debug(f"Avoided using username as project name")

        # Skip common project container directories
        project_containers = ['Documents', 'Projects', 'Code', 'workspace', 'repos', 'git', 'src', 'codebase']
        if project_name in project_containers:
            # Don't use container directories as project names
            # Try to use the next component if available
            container_index = path_parts.index(project_name)
            if container_index + 1 < len(path_parts):
                project_name = path_parts[container_index + 1]
                if debug:
                    logger.debug(f"Skipped container dir, using next component as project name: {project_name}")

        # If we still don't have a project name, use the first non-system directory after username
        if not project_name and username_index + 1 < len(path_parts):
            system_dirs = ['Library', 'Applications', 'System', 'var', 'opt', 'tmp']
            for i in range(username_index + 1, len(path_parts)):
                if path_parts[i] not in system_dirs and path_parts[i] not in project_containers:
                    project_name = path_parts[i]
                    if debug:
                        logger.debug(f"Using non-system dir as project name: {project_name}")
                    break
    else:
        # If not in a user directory, use the basename
        project_name = path_parts[-1] if path_parts else "Root"
        if debug:
            logger.debug(f"Using basename as project name: {project_name}")

    # Final check: don't return username as project name
    if project_name == current_username:
        project_name = "Home Directory"
        if debug:
            logger.debug(f"Final check: replaced username with 'Home Directory'")

    return project_name if project_name else "Unknown Project"

def workspace_info(db: pathlib.Path):
    try:
        con = sqlite3.connect(f"file:{db}?mode=ro", uri=True)
        cur = con.cursor()

        # Get file paths from history entries to extract the project name
        proj = {"name": "(unknown)", "rootPath": "(unknown)"}
        ents = j(cur,"ItemTable","history.entries") or []

        logger.info(f"提取工作区信息 - 数据库: {db}")

        # Extract file paths from history entries, stripping the file:/// scheme
        paths = []
        for e in ents:
            resource = e.get("editor", {}).get("resource", "")
            if resource and resource.startswith("file:///"):
                paths.append(resource[len("file:///"):])

        # If we found file paths, extract the project name using the longest common prefix
        if paths:
            logger.info(f"从history.entries找到 {len(paths)} 个文件路径")
            if DETAILED_LOGGING and len(paths) > 0:
                logger.info(f"示例路径: {paths[0]}")

            # Get the longest common prefix
            common_prefix = os.path.commonprefix(paths)
            logger.info(f"文件路径的共同前缀: {common_prefix}")

            # Find the last directory separator in the common prefix
            last_separator_index = common_prefix.rfind('/')
            if last_separator_index > 0:
                project_root = common_prefix[:last_separator_index]
                logger.info(f"从共同前缀提取的项目根目录: {project_root}")

                # Extract the project name using the helper function
                project_name = extract_project_name_from_path(project_root, debug=True)
                logger.info(f"从项目根目录提取的项目名称: {project_name}")

                proj = {"name": project_name, "rootPath": "/" + project_root.lstrip('/')}
                logger.info(f"从history.entries设置项目信息: name={project_name}, rootPath=/{project_root.lstrip('/')}")

        # Try backup methods if we didn't get a project name
        if proj["name"] == "(unknown)":
            logger.info("项目名称未知，尝试备用方法")

            # Check debug.selectedroot as a fallback
            selected_root = j(cur, "ItemTable", "debug.selectedroot")
            if selected_root and isinstance(selected_root, str) and selected_root.startswith("file:///"):
                path = selected_root[len("file:///"):]
                if path:
                    root_path = "/" + path.strip("/")
                    logger.info(f"从debug.selectedroot获取项目根目录: {root_path}")

                    # Extract the project name using the helper function
                    project_name = extract_project_name_from_path(root_path, debug=True)
                    logger.info(f"从debug.selectedroot提取的项目名称: {project_name}")

                    if project_name:
                        proj = {"name": project_name, "rootPath": root_path}
                        logger.info(f"从debug.selectedroot设置项目信息: name={project_name}, rootPath={root_path}")

        # composers meta
        comp_meta={}
        cd = j(cur,"ItemTable","composer.composerData") or {}
        for c in cd.get("allComposers",[]):
            comp_meta[c["composerId"]] = {
                "title": c.get("name","(untitled)"),
                "createdAt": c.get("createdAt"),
                "lastUpdatedAt": c.get("lastUpdatedAt")
            }

        # Try to get composer info from workbench.panel.aichat.view.aichat.chatdata
        chat_data = j(cur, "ItemTable", "workbench.panel.aichat.view.aichat.chatdata") or {}
        for tab in chat_data.get("tabs", []):
            tab_id = tab.get("tabId")
            if tab_id and tab_id not in comp_meta:
                comp_meta[tab_id] = {
                    "title": f"Chat {tab_id[:8]}",
                    "createdAt": None,
                    "lastUpdatedAt": None
                }
    except sqlite3.DatabaseError as e:
        logger.debug(f"Error getting workspace info from {db}: {e}")
        proj = {"name": "(unknown)", "rootPath": "(unknown)"}
        comp_meta = {}
    finally:
        if 'con' in locals():
            con.close()

    logger.info(f"工作区信息提取结果: {proj}")
    return proj, comp_meta

################################################################################
# GlobalStorage
################################################################################
def global_storage_path(base: pathlib.Path) -> pathlib.Path:
    """Return path to the global storage state.vscdb."""
    if DETAILED_LOGGING:
        logger.info(f"Searching for global storage in: {base}")

    # Check if globalStorage directory exists
    global_storage_dir = base / "User" / "globalStorage"
    if global_storage_dir.exists():
        if DETAILED_LOGGING:
            logger.info(f"Found globalStorage directory: {global_storage_dir}")
            # List all directories in globalStorage
            try:
                subdirs = [d.name for d in global_storage_dir.iterdir() if d.is_dir()]
                logger.info(f"Subdirectories in globalStorage: {subdirs}")
            except Exception as e:
                logger.error(f"Error listing globalStorage subdirectories: {e}")
    else:
        if DETAILED_LOGGING:
            logger.warning(f"globalStorage directory not found at: {global_storage_dir}")

    # First check for state.vscdb in globalStorage
    global_db = base / "User" / "globalStorage" / "state.vscdb"
    if global_db.exists():
        if DETAILED_LOGGING:
            logger.info(f"Found global storage database: {global_db}")
        return global_db
    elif DETAILED_LOGGING:
        logger.info(f"No state.vscdb found in globalStorage")

    # Legacy paths
    g_dirs = [
        base / "User" / "globalStorage" / "cursor.cursor",
        base / "User" / "globalStorage" / "cursor",
        # Try directly in globalStorage as well
        base / "User" / "globalStorage"
    ]

    for d in g_dirs:
        if d.exists():
            if DETAILED_LOGGING:
                logger.info(f"Checking directory: {d}")

            # Look for SQLite files
            sqlite_files = list(d.glob("*.sqlite"))
            if sqlite_files:
                if DETAILED_LOGGING:
                    logger.info(f"Found SQLite files in {d}: {[f.name for f in sqlite_files]}")
                return sqlite_files[0]  # Return the first one
            elif DETAILED_LOGGING:
                logger.info(f"No SQLite files found in {d}")

            # Also try other extensions
            for ext in ["*.db", "*.sqlite3", "*.vscdb"]:
                other_files = list(d.glob(ext))
                if other_files:
                    if DETAILED_LOGGING:
                        logger.info(f"Found {ext} files in {d}: {[f.name for f in other_files]}")
                    return other_files[0]  # Return the first one
        elif DETAILED_LOGGING:
            logger.info(f"Directory does not exist: {d}")

    if DETAILED_LOGGING:
        logger.warning("No global storage database found")
    return None

################################################################################
# Extraction pipeline
################################################################################
def extract_chats() -> list[Dict[str,Any]]:
    root = cursor_root()
    logger.info(f"Using Cursor root: {root}")

    if DETAILED_LOGGING:
        # Log the directory structure for debugging
        logger.info("Cursor directory structure:")
        try:
            # Check if User directory exists
            user_dir = root / "User"
            if user_dir.exists():
                logger.info(f"User directory exists: {user_dir}")

                # Check workspaceStorage
                workspace_storage = user_dir / "workspaceStorage"
                if workspace_storage.exists():
                    workspace_dirs = [d.name for d in workspace_storage.iterdir() if d.is_dir()]
                    logger.info(f"Workspace directories: {workspace_dirs}")
                else:
                    logger.warning(f"workspaceStorage directory not found: {workspace_storage}")

                # Check globalStorage
                global_storage = user_dir / "globalStorage"
                if global_storage.exists():
                    global_dirs = [d.name for d in global_storage.iterdir() if d.is_dir()]
                    logger.info(f"Global storage directories: {global_dirs}")

                    # Check for database files directly in globalStorage
                    db_files = list(global_storage.glob("*.sqlite")) + list(global_storage.glob("*.db")) + list(global_storage.glob("*.vscdb"))
                    if db_files:
                        logger.info(f"Database files in globalStorage: {[f.name for f in db_files]}")
                else:
                    logger.warning(f"globalStorage directory not found: {global_storage}")
            else:
                logger.warning(f"User directory not found: {user_dir}")
        except Exception as e:
            logger.error(f"Error examining Cursor directory structure: {e}")

    # Diagnostic: Check for AI-related keys in the first workspace
    if os.environ.get("CURSOR_CHAT_DIAGNOSTICS"):
        try:
            first_ws = next(workspaces(root))
            if first_ws:
                ws_id, db = first_ws
                logger.debug(f"\n--- DIAGNOSTICS for workspace {ws_id} ---")
                con = sqlite3.connect(f"file:{db}?mode=ro", uri=True)
                cur = con.cursor()

                # List all tables
                cur.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cur.fetchall()]
                logger.debug(f"Tables in workspace DB: {tables}")

                # Search for AI-related keys
                if "ItemTable" in tables:
                    for pattern in ['%ai%', '%chat%', '%composer%', '%prompt%', '%generation%']:
                        cur.execute("SELECT key FROM ItemTable WHERE key LIKE ?", (pattern,))
                        keys = [row[0] for row in cur.fetchall()]
                        if keys:
                            logger.debug(f"Keys matching '{pattern}': {keys}")

                con.close()

            # Check global storage
            global_db = global_storage_path(root)
            if global_db:
                logger.debug(f"\n--- DIAGNOSTICS for global storage ---")
                con = sqlite3.connect(f"file:{global_db}?mode=ro", uri=True)
                cur = con.cursor()

                # List all tables
                cur.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cur.fetchall()]
                logger.debug(f"Tables in global DB: {tables}")

                # Search for AI-related keys in ItemTable
                if "ItemTable" in tables:
                    for pattern in ['%ai%', '%chat%', '%composer%', '%prompt%', '%generation%']:
                        cur.execute("SELECT key FROM ItemTable WHERE key LIKE ?", (pattern,))
                        keys = [row[0] for row in cur.fetchall()]
                        if keys:
                            logger.debug(f"Keys matching '{pattern}': {keys}")

                # Check for keys in cursorDiskKV
                if "cursorDiskKV" in tables:
                    cur.execute("SELECT DISTINCT substr(key, 1, instr(key, ':') - 1) FROM cursorDiskKV")
                    prefixes = [row[0] for row in cur.fetchall()]
                    logger.debug(f"Key prefixes in cursorDiskKV: {prefixes}")

                con.close()

            logger.debug("\n--- END DIAGNOSTICS ---\n")
        except Exception as e:
            logger.debug(f"Error in diagnostics: {e}")

    # map lookups
    ws_proj  : Dict[str,Dict[str,Any]] = {}
    comp_meta: Dict[str,Dict[str,Any]] = {}
    comp2ws  : Dict[str,str]           = {}
    sessions : Dict[str,Dict[str,Any]] = defaultdict(lambda: {"messages":[]})

    # 1. Process workspace DBs first
    logger.debug("Processing workspace databases...")
    ws_count = 0
    ws_session_count = 0  # 跟踪从工作区提取的会话数量
    for ws_id, db in workspaces(root):
        ws_count += 1
        logger.debug(f"Processing workspace {ws_id} - {db}")
        proj, meta = workspace_info(db)
        ws_proj[ws_id] = proj
        for cid, m in meta.items():
            comp_meta[cid] = m
            comp2ws[cid] = ws_id

        # Extract chat data from workspace's state.vscdb
        msg_count = 0
        session_count_before = len(set(cid for cid in sessions.keys() if sessions[cid]["messages"]))
        for cid, role, text, db_path in iter_chat_from_item_table(db):
            # Add the message
            sessions[cid]["messages"].append({"role": role, "content": text})
            # Make sure to record the database path
            if "db_path" not in sessions[cid]:
                sessions[cid]["db_path"] = db_path
            msg_count += 1
            if cid not in comp_meta:
                comp_meta[cid] = {"title": f"Chat {cid[:8]}", "createdAt": None, "lastUpdatedAt": None}
                comp2ws[cid] = ws_id

        # 计算此工作区新增的会话数量
        session_count_after = len(set(cid for cid in sessions.keys() if sessions[cid]["messages"]))
        new_sessions = session_count_after - session_count_before
        ws_session_count += new_sessions

        if msg_count > 0:
            logger.info(f"  - 工作区 {ws_id}: 提取了 {msg_count} 条消息，新增 {new_sessions} 个会话")
        else:
            logger.debug(f"  - 工作区 {ws_id}: 未提取到消息")

    if ws_session_count > 0:
        logger.info(f"从 {ws_count} 个工作区共提取了 {ws_session_count} 个会话")
    else:
        logger.debug(f"处理了 {ws_count} 个工作区，但未提取到会话")

    # 2. Process global storage
    global_db = global_storage_path(root)
    if global_db:
        logger.debug(f"Processing global storage: {global_db}")

        # 记录处理前的会话数量
        sessions_before_global = set(cid for cid in sessions.keys() if sessions[cid]["messages"])
        logger.info(f"处理全局存储前已有 {len(sessions_before_global)} 个会话")

        # Extract bubbles from cursorDiskKV
        msg_count = 0
        bubble_session_count_before = len(set(cid for cid in sessions.keys() if sessions[cid]["messages"]))

        for cid, role, text, db_path in iter_bubbles_from_disk_kv(global_db):
            sessions[cid]["messages"].append({"role": role, "content": text})
            # Record the database path
            if "db_path" not in sessions[cid]:
                sessions[cid]["db_path"] = db_path
            msg_count += 1
            if cid not in comp_meta:
                comp_meta[cid] = {"title": f"Chat {cid[:8]}", "createdAt": None, "lastUpdatedAt": None}
                comp2ws[cid] = "(global)"

        bubble_session_count_after = len(set(cid for cid in sessions.keys() if sessions[cid]["messages"]))
        bubble_new_sessions = bubble_session_count_after - bubble_session_count_before
        logger.info(f"  - 从全局 cursorDiskKV bubbles 提取了 {msg_count} 条消息，新增 {bubble_new_sessions} 个会话")

        # Extract composer data
        comp_count = 0
        composer_session_count_before = len(set(cid for cid in sessions.keys() if sessions[cid]["messages"]))

        for cid, data, db_path in iter_composer_data(global_db):
            if cid not in comp_meta:
                created_at = data.get("createdAt")
                comp_meta[cid] = {
                    "title": f"Chat {cid[:8]}",
                    "createdAt": created_at,
                    "lastUpdatedAt": created_at
                }
                comp2ws[cid] = "(global)"

            # Record the database path
            if "db_path" not in sessions[cid]:
                sessions[cid]["db_path"] = db_path

            # Extract conversation from composer data
            conversation = data.get("conversation", [])
            if conversation:
                msg_count = 0
                for msg in conversation:
                    msg_type = msg.get("type")
                    if msg_type is None:
                        continue

                    # Type 1 = user, Type 2 = assistant
                    role = "user" if msg_type == 1 else "assistant"
                    content = msg.get("text", "")
                    if content and isinstance(content, str):
                        sessions[cid]["messages"].append({"role": role, "content": content})
                        msg_count += 1

                if msg_count > 0:
                    comp_count += 1
                    logger.info(f"  - 添加了 {msg_count} 条消息，来自 composer {cid[:8]}")

        composer_session_count_after = len(set(cid for cid in sessions.keys() if sessions[cid]["messages"]))
        composer_new_sessions = composer_session_count_after - composer_session_count_before

        if comp_count > 0:
            logger.info(f"  - 从全局 cursorDiskKV 中的 {comp_count} 个 composers 提取了数据，新增 {composer_new_sessions} 个会话")

        # Also try ItemTable in global DB
        try:
            item_table_session_count_before = len(set(cid for cid in sessions.keys() if sessions[cid]["messages"]))

            con = sqlite3.connect(f"file:{global_db}?mode=ro", uri=True)
            chat_data = j(con.cursor(), "ItemTable", "workbench.panel.aichat.view.aichat.chatdata")
            if chat_data:
                msg_count = 0
                tab_count = 0

                for tab in chat_data.get("tabs", []):
                    tab_id = tab.get("tabId")
                    if tab_id and tab_id not in comp_meta:
                        comp_meta[tab_id] = {
                            "title": f"Global Chat {tab_id[:8]}",
                            "createdAt": None,
                            "lastUpdatedAt": None
                        }
                        comp2ws[tab_id] = "(global)"

                    tab_msg_count = 0
                    for bubble in tab.get("bubbles", []):
                        content = ""
                        if "text" in bubble:
                            content = bubble["text"]
                        elif "content" in bubble:
                            content = bubble["content"]

                        if content and isinstance(content, str):
                            role = "user" if bubble.get("type") == "user" else "assistant"
                            sessions[tab_id]["messages"].append({"role": role, "content": content})
                            msg_count += 1
                            tab_msg_count += 1

                    if tab_msg_count > 0:
                        tab_count += 1
                        logger.info(f"  - 从全局 ItemTable 标签页 {tab_id[:8]} 提取了 {tab_msg_count} 条消息")

                item_table_session_count_after = len(set(cid for cid in sessions.keys() if sessions[cid]["messages"]))
                item_table_new_sessions = item_table_session_count_after - item_table_session_count_before

                logger.info(f"  - 从全局 ItemTable 的 {tab_count} 个标签页提取了 {msg_count} 条消息，新增 {item_table_new_sessions} 个会话")
            con.close()

            # 从全局 ItemTable 中提取其他聊天数据
            other_msg_count = 0
            other_session_count_before = len(set(cid for cid in sessions.keys() if sessions[cid]["messages"]))

            for cid, role, text, db_path in iter_chat_from_item_table(global_db):
                sessions[cid]["messages"].append({"role": role, "content": text})
                if "db_path" not in sessions[cid]:
                    sessions[cid]["db_path"] = db_path
                other_msg_count += 1
                if cid not in comp_meta:
                    comp_meta[cid] = {"title": f"Global Chat {cid[:8]}", "createdAt": None, "lastUpdatedAt": None}
                    comp2ws[cid] = "(global)"

            other_session_count_after = len(set(cid for cid in sessions.keys() if sessions[cid]["messages"]))
            other_new_sessions = other_session_count_after - other_session_count_before

            if other_msg_count > 0:
                logger.info(f"  - 从全局 ItemTable 其他来源提取了 {other_msg_count} 条消息，新增 {other_new_sessions} 个会话")

        except Exception as e:
            logger.debug(f"Error processing global ItemTable: {e}")

        # 汇总全局存储的会话统计
        sessions_after_global = set(cid for cid in sessions.keys() if sessions[cid]["messages"])
        total_new_sessions = len(sessions_after_global) - len(sessions_before_global)
        logger.info(f"从全局存储总共新增了 {total_new_sessions} 个会话，当前总会话数: {len(sessions_after_global)}")

    # 3. Build final list
    out = []
    for cid, data in sessions.items():
        if not data["messages"]:
            continue
        ws_id = comp2ws.get(cid, "(unknown)")
        project = ws_proj.get(ws_id, {"name": "(unknown)", "rootPath": "(unknown)"})
        meta = comp_meta.get(cid, {"title": "(untitled)", "createdAt": None, "lastUpdatedAt": None})

        logger.info(f"构建会话 {cid[:8]} 的输出数据:")
        logger.info(f"  - 关联的工作区ID: {ws_id}")
        logger.info(f"  - 项目信息来源: {'工作区映射' if ws_id in ws_proj else '默认值'}")
        logger.info(f"  - 项目信息: {project}")
        logger.info(f"  - 消息数量: {len(data['messages'])}")

        # 添加工作区ID到项目信息中
        project["workspace_id"] = ws_id

        # Create the output object with the db_path included
        chat_data = {
            "project": project,
            "session": {"composerId": cid, **meta},
            "messages": data["messages"],
            "workspace_id": ws_id,
        }

        # Add the database path if available
        if "db_path" in data:
            chat_data["db_path"] = data["db_path"]
            logger.info(f"  - 数据库路径: {data['db_path']}")

        out.append(chat_data)

    # Sort by last updated time if available
    out.sort(key=lambda s: s["session"].get("lastUpdatedAt") or 0, reverse=True)
    logger.info(f"Total chat sessions extracted: {len(out)}")
    return out

def extract_project_from_git_repos(workspace_id, debug=False):
    """
    Extract project name from the git repositories in a workspace.
    Returns None if no repositories found or unable to access the DB.
    """
    if not workspace_id or workspace_id == "unknown" or workspace_id == "(unknown)" or workspace_id == "(global)":
        if debug:
            logger.debug(f"Invalid workspace ID: {workspace_id}")
        return None

    # Find the workspace DB
    cursor_base = cursor_root()
    workspace_db_path = cursor_base / "User" / "workspaceStorage" / workspace_id / "state.vscdb"

    if not workspace_db_path.exists():
        if debug:
            logger.debug(f"Workspace DB not found for ID: {workspace_id}")
        return None

    try:
        # Connect to the workspace DB
        if debug:
            logger.debug(f"Connecting to workspace DB: {workspace_db_path}")
        con = sqlite3.connect(f"file:{workspace_db_path}?mode=ro", uri=True)
        cur = con.cursor()

        # Look for git repositories
        git_data = j(cur, "ItemTable", "scm:view:visibleRepositories")
        if not git_data or not isinstance(git_data, dict) or 'all' not in git_data:
            if debug:
                logger.debug(f"No git repositories found in workspace {workspace_id}, git_data: {git_data}")
            con.close()
            return None

        # Extract repo paths from the 'all' key
        repos = git_data.get('all', [])
        if not repos or not isinstance(repos, list):
            if debug:
                logger.debug(f"No repositories in 'all' key for workspace {workspace_id}, repos: {repos}")
            con.close()
            return None

        if debug:
            logger.debug(f"Found {len(repos)} git repositories in workspace {workspace_id}: {repos}")

        # Process each repo path
        for repo in repos:
            if not isinstance(repo, str):
                continue

            # Look for git:Git:file:/// pattern
            if "git:Git:file:///" in repo:
                # Extract the path part
                path = repo.split("file:///")[-1]
                path_parts = [p for p in path.split('/') if p]

                if path_parts:
                    # Use the last part as the project name
                    project_name = path_parts[-1]
                    if debug:
                        logger.debug(f"Found project name '{project_name}' from git repo in workspace {workspace_id}")
                    con.close()
                    return project_name
            else:
                if debug:
                    logger.debug(f"No 'git:Git:file:///' pattern in repo: {repo}")

        if debug:
            logger.debug(f"No suitable git repos found in workspace {workspace_id}")
        con.close()
    except Exception as e:
        if debug:
            logger.debug(f"Error extracting git repos from workspace {workspace_id}: {e}")
        return None

    return None

def format_chat_for_frontend(chat):
    """Format the chat data to match what the frontend expects."""
    try:
        # Generate a unique ID for this chat if it doesn't have one
        session_id = str(uuid.uuid4())
        if 'session' in chat and chat['session'] and isinstance(chat['session'], dict):
            session_id = chat['session'].get('composerId', session_id)

        logger.info(f"格式化会话 {session_id[:8]} 的数据")

        # Format date from createdAt timestamp or use current date
        date = int(datetime.datetime.now().timestamp())
        if 'session' in chat and chat['session'] and isinstance(chat['session'], dict):
            created_at = chat['session'].get('createdAt')
            if created_at and isinstance(created_at, (int, float)):
                # Convert from milliseconds to seconds
                date = created_at / 1000
                logger.info(f"  - 使用会话的createdAt作为日期: {datetime.datetime.fromtimestamp(date).strftime('%Y-%m-%d %H:%M:%S')}")
            else:
                logger.info(f"  - 使用当前时间作为日期: {datetime.datetime.fromtimestamp(date).strftime('%Y-%m-%d %H:%M:%S')}")

        # Ensure project has expected fields
        project = chat.get('project', {})
        if not isinstance(project, dict):
            project = {}

        logger.info(f"  - 原始项目信息: {project}")

        # Get workspace_id from chat
        workspace_id = chat.get('workspace_id', 'unknown')
        logger.info(f"  - 工作区ID: {workspace_id}")

        # Get the database path information
        db_path = chat.get('db_path', 'Unknown database path')
        logger.info(f"  - 数据库路径: {db_path}")

        # If project name is a username or unknown, try to extract a better name from rootPath
        if project.get('rootPath'):
            current_name = project.get('name', '')
            username = os.path.basename(os.path.expanduser('~'))
            logger.info(f"  - 当前项目名称: {current_name}, 用户名: {username}")

            # Check if project name is username or unknown or very generic
            if (current_name == username or
                current_name == '(unknown)' or
                current_name == 'Root' or
                # Check if rootPath is directly under /Users/<USER>
                (project.get('rootPath').startswith(f'/Users/<USER>') and
                 project.get('rootPath').count('/') <= 3)):

                logger.info(f"  - 项目名称需要改进，尝试从路径提取")
                # Try to extract a better name from the path
                project_name = extract_project_name_from_path(project.get('rootPath'), debug=False)
                logger.info(f"  - 从路径提取的项目名称: {project_name}")

                # Only use the new name if it's meaningful
                if (project_name and
                    project_name != 'Unknown Project' and
                    project_name != username and
                    project_name not in ['Documents', 'Downloads', 'Desktop']):

                    logger.info(f"  - 改进项目名称: '{current_name}' -> '{project_name}'")
                    project['name'] = project_name
                elif project.get('rootPath').startswith(f'/Users/<USER>/Documents/codebase/'):
                    # Special case for /Users/<USER>/Documents/codebase/X
                    parts = project.get('rootPath').split('/')
                    if len(parts) > 5:  # /Users/<USER>/Documents/codebase/X
                        project['name'] = parts[5]
                        logger.info(f"  - 设置项目名称为codebase子目录: {parts[5]}")
                    else:
                        project['name'] = "cursor-view"  # Current project as default
                        logger.info(f"  - 设置项目名称为默认值: cursor-view")

        # If the project doesn't have a rootPath or it's very generic, enhance it with workspace_id
        if not project.get('rootPath') or project.get('rootPath') == '/' or project.get('rootPath') == '/Users':
            logger.info(f"  - 项目根路径缺失或过于通用: {project.get('rootPath')}")
            if workspace_id != 'unknown':
                # Use workspace_id to create a more specific path
                if not project.get('rootPath'):
                    project['rootPath'] = f"/workspace/{workspace_id}"
                    logger.info(f"  - 设置项目根路径为: /workspace/{workspace_id}")
                elif project.get('rootPath') == '/' or project.get('rootPath') == '/Users':
                    project['rootPath'] = f"{project['rootPath']}/workspace/{workspace_id}"
                    logger.info(f"  - 增强项目根路径为: {project['rootPath']}")

        # FALLBACK: If project name is still generic, try to extract it from git repositories
        if project.get('name') in ['Home Directory', '(unknown)']:
            logger.info(f"  - 项目名称仍然通用，尝试从git仓库提取")
            git_project_name = extract_project_from_git_repos(workspace_id, debug=True)
            if git_project_name:
                logger.info(f"  - 使用git仓库改进项目名称: '{project.get('name')}' -> '{git_project_name}'")
                project['name'] = git_project_name

        # Add workspace_id to the project data explicitly
        project['workspace_id'] = workspace_id
        logger.info(f"  - 添加工作区ID到项目数据: {workspace_id}")

        # Ensure messages exist and are properly formatted
        messages = chat.get('messages', [])
        if not isinstance(messages, list):
            messages = []

        logger.info(f"  - 消息数量: {len(messages)}")

        # Create properly formatted chat object
        formatted_chat = {
            'project': project,
            'messages': messages,
            'date': date,
            'session_id': session_id,
            'workspace_id': workspace_id,
            'db_path': db_path  # Include the database path in the output
        }

        logger.info(f"  - 最终项目信息: {project}")
        return formatted_chat
    except Exception as e:
        logger.error(f"Error formatting chat: {e}")
        # Return a minimal valid object if there's an error
        return {
            'project': {'name': 'Error', 'rootPath': '/'},
            'messages': [],
            'date': int(datetime.datetime.now().timestamp()),
            'session_id': str(uuid.uuid4()),
            'workspace_id': 'error',
            'db_path': 'Error retrieving database path'
        }

@app.route('/api/chats', methods=['GET'])
def get_chats():
    """Get all chat sessions."""
    try:
        logger.info(f"Received request for chats from {request.remote_addr}")
        chats = extract_chats()
        logger.info(f"Retrieved {len(chats)} chats")

        # Format each chat for the frontend
        formatted_chats = []
        for chat in chats:
            try:
                formatted_chat = format_chat_for_frontend(chat)
                formatted_chats.append(formatted_chat)
            except Exception as e:
                logger.error(f"Error formatting individual chat: {e}")
                # Skip this chat if it can't be formatted
                continue

        logger.info(f"Returning {len(formatted_chats)} formatted chats")
        return jsonify(formatted_chats)
    except Exception as e:
        logger.error(f"Error in get_chats: {e}", exc_info=True)
        return jsonify({"error": str(e)}), 500

@app.route('/api/chat/<session_id>', methods=['GET'])
def get_chat(session_id):
    """Get a specific chat session by ID."""
    try:
        logger.info(f"Received request for chat {session_id} from {request.remote_addr}")
        chats = extract_chats()

        for chat in chats:
            # Check for a matching composerId safely
            if 'session' in chat and chat['session'] and isinstance(chat['session'], dict):
                if chat['session'].get('composerId') == session_id:
                    formatted_chat = format_chat_for_frontend(chat)
                    return jsonify(formatted_chat)

        logger.warning(f"Chat with ID {session_id} not found")
        return jsonify({"error": "Chat not found"}), 404
    except Exception as e:
        logger.error(f"Error in get_chat: {e}", exc_info=True)
        return jsonify({"error": str(e)}), 500

@app.route('/api/chat/<session_id>/export', methods=['GET'])
def export_chat(session_id):
    """Export a specific chat session as standalone HTML (TEMP: text/plain)."""
    logger.critical(f"!!!!!!!! EXPORT_CHAT ENDPOINT HIT for session: {session_id} !!!!!!!!")
    try:
        logger.info(f"Received request to export chat {session_id} from {request.remote_addr}")
        chats = extract_chats()

        for chat in chats:
            # Check for a matching composerId safely
            if 'session' in chat and chat['session'] and isinstance(chat['session'], dict):
                if chat['session'].get('composerId') == session_id:
                    formatted_chat = format_chat_for_frontend(chat)

                    html_content = generate_standalone_html(formatted_chat)
                    return Response(
                        html_content,
                        mimetype="text/html; charset=utf-8",
                        headers={
                            "Content-Disposition": f'attachment; filename="cursor-chat-{session_id[:8]}.html"',
                            "Content-Length": str(len(html_content)),
                            "Cache-Control": "no-store",
                        },
                    )

        logger.warning(f"Chat with ID {session_id} not found for export")
        return jsonify({"error": "Chat not found"}), 404
    except Exception as e:
        logger.error(f"Error in export_chat: {e}", exc_info=True)
        return jsonify({"error": str(e)}), 500

def generate_standalone_html(chat):
    """Generate a standalone HTML representation of the chat."""
    logger.info(f"Generating HTML for session ID: {chat.get('session_id', 'N/A')}")
    try:
        # Format date for display
        date_display = "Unknown date"
        if chat.get('date'):
            try:
                date_obj = datetime.datetime.fromtimestamp(chat['date'])
                date_display = date_obj.strftime("%Y-%m-%d %H:%M:%S")
            except Exception as e:
                logger.warning(f"Error formatting date: {e}")

        # Get project info
        project_name = chat.get('project', {}).get('name', 'Unknown Project')
        project_path = chat.get('project', {}).get('rootPath', 'Unknown Path')
        logger.info(f"Project: {project_name}, Path: {project_path}, Date: {date_display}")

        # Build the HTML content
        messages_html = ""
        messages = chat.get('messages', [])
        logger.info(f"Found {len(messages)} messages for the chat.")

        if not messages:
            logger.warning("No messages found in the chat object to generate HTML.")
            messages_html = "<p>No messages found in this conversation.</p>"
        else:
            for i, msg in enumerate(messages):
                role = msg.get('role', 'unknown')
                content = msg.get('content', '')
                logger.debug(f"Processing message {i+1}/{len(messages)} - Role: {role}, Content length: {len(content)}")

                if not content or not isinstance(content, str):
                    logger.warning(f"Message {i+1} has invalid content: {content}")
                    content = "Content unavailable"

                # Simple HTML escaping
                escaped_content = content.replace("&", "&amp;").replace("<", "&lt;").replace(">", "&gt;")

                # Convert markdown code blocks (handle potential nesting issues simply)
                processed_content = ""
                in_code_block = False
                for line in escaped_content.split('\n'):
                    if line.strip().startswith("```"):
                        if not in_code_block:
                            processed_content += "<pre><code>"
                            in_code_block = True
                            # Remove the first ``` marker
                            line = line.strip()[3:]
                        else:
                            processed_content += "</code></pre>\n"
                            in_code_block = False
                            line = "" # Skip the closing ``` line

                    if in_code_block:
                         # Inside code block, preserve spacing and add line breaks
                        processed_content += line + "\n"
                    else:
                        # Outside code block, use <br> for newlines
                        processed_content += line + "<br>"

                # Close any unclosed code block at the end
                if in_code_block:
                    processed_content += "</code></pre>"

                avatar = "👤" if role == "user" else "🤖"
                name = "You" if role == "user" else "Cursor Assistant"
                bg_color = "#f0f7ff" if role == "user" else "#f0fff7"
                border_color = "#3f51b5" if role == "user" else "#00796b"

                messages_html += f"""
                <div class="message" style="margin-bottom: 20px;">
                    <div class="message-header" style="display: flex; align-items: center; margin-bottom: 8px;">
                        <div class="avatar" style="width: 32px; height: 32px; border-radius: 50%; background-color: {border_color}; color: white; display: flex; justify-content: center; align-items: center; margin-right: 10px;">
                            {avatar}
                        </div>
                        <div class="sender" style="font-weight: bold;">{name}</div>
                    </div>
                    <div class="message-content" style="padding: 15px; border-radius: 8px; background-color: {bg_color}; border-left: 4px solid {border_color}; margin-left: {0 if role == 'user' else '40px'}; margin-right: {0 if role == 'assistant' else '40px'};">
                        {processed_content}
                    </div>
                </div>
                """

        # Create the complete HTML document
        html = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cursor Chat - {project_name}</title>
    <style>
        body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; line-height: 1.6; color: #333; max-width: 900px; margin: 20px auto; padding: 20px; border: 1px solid #eee; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }}
        h1, h2, h3 {{ color: #2c3e50; }}
        .header {{ background: linear-gradient(90deg, #f0f7ff 0%, #f0fff7 100%); color: white; padding: 15px 20px; border-radius: 8px 8px 0 0; margin: -20px -20px 20px -20px; }}
        .chat-info {{ display: flex; flex-wrap: wrap; gap: 10px 20px; margin-bottom: 20px; background-color: #f9f9f9; padding: 12px 15px; border-radius: 8px; font-size: 0.9em; }}
        .info-item {{ display: flex; align-items: center; }}
        .info-label {{ font-weight: bold; margin-right: 5px; color: #555; }}
        pre {{ background-color: #eef; padding: 15px; border-radius: 5px; overflow-x: auto; border: 1px solid #ddd; font-family: 'Courier New', Courier, monospace; font-size: 0.9em; white-space: pre-wrap; word-wrap: break-word; }}
        code {{ background-color: transparent; padding: 0; border-radius: 0; font-family: inherit; }}
        .message-content pre code {{ background-color: transparent; }}
        .message-content {{ word-wrap: break-word; overflow-wrap: break-word; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Cursor Chat: {project_name}</h1>
    </div>
    <div class="chat-info">
        <div class="info-item"><span class="info-label">Project:</span> <span>{project_name}</span></div>
        <div class="info-item"><span class="info-label">Path:</span> <span>{project_path}</span></div>
        <div class="info-item"><span class="info-label">Date:</span> <span>{date_display}</span></div>
        <div class="info-item"><span class="info-label">Session ID:</span> <span>{chat.get('session_id', 'Unknown')}</span></div>
    </div>
    <h2>Conversation History</h2>
    <div class="messages">
{messages_html}
    </div>
    <div style="margin-top: 30px; font-size: 12px; color: #999; text-align: center; border-top: 1px solid #eee; padding-top: 15px;">
        <a href="https://github.com/saharmor/cursor-view" target="_blank" rel="noopener noreferrer">Exported from Cursor View</a>
    </div>
</body>
</html>"""

        logger.info(f"Finished generating HTML. Total length: {len(html)}")
        return html
    except Exception as e:
        logger.error(f"Error generating HTML for session {chat.get('session_id', 'N/A')}: {e}", exc_info=True)
        # Return an HTML formatted error message
        return f"<html><body><h1>Error generating chat export</h1><p>Error: {e}</p></body></html>"

# Serve React app
@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def serve_react(path):
    if path and Path(app.static_folder, path).exists():
        return send_from_directory(app.static_folder, path)
    return send_from_directory(app.static_folder, 'index.html')

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Run the Cursor Chat View server')
    parser.add_argument('--port', type=int, default=5000, help='Port to run the server on')
    parser.add_argument('--debug', action='store_true', help='Run in debug mode')
    args = parser.parse_args()

    logger.info(f"Starting server on port {args.port}")
    app.run(debug=args.debug, port=args.port)